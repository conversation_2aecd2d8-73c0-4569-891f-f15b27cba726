absl-py==2.0.0
accelerate==1.0.1
aiofiles==23.2.1
albumentations==1.3.1
annotated-types==0.7.0
antlr4-python3-runtime==4.8
anyio==4.5.2
astunparse==1.6.3
audioread==3.0.1
av==11.0.0
beautifulsoup4==4.12.3
bitarray==2.8.2
boto3==1.28.78
botocore==1.31.78
cachetools==4.2.4
certifi==2023.7.22
cffi==1.16.0
charset-normalizer==3.2.0
click==8.1.7
cmake==3.30.1
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.1.1
cycler==0.12.1
Cython==3.0.5
decorator==4.4.2
decord==0.6.0
einops==0.7.0
einops-exts==0.0.4
exceptiongroup==1.2.2
fairseq==0.12.2
fastapi==0.115.8
ffmpeg==1.4
ffmpeg-python==0.2.0
ffmpy==0.5.0
filelock==3.13.1
flatbuffers==23.5.26
flow-vis==0.1
fonttools==4.44.0
fsspec==2023.10.0
future==1.0.0
gast==0.4.0
gdown==5.1.0
google-auth==2.32.0
google-auth-oauthlib==0.4.6
google-pasta==0.2.0
gradio==4.44.1
gradio_client==1.3.0
grpcio==1.59.2
h11==0.14.0
h5py==3.10.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.28.1
humanfriendly==10.0
hydra-core==1.0.7
idna==3.4
imageio==2.31.5
imageio-ffmpeg==0.4.9
importlib-metadata==6.8.0
importlib-resources==6.1.0
Jinja2==3.1.4
jmespath==1.0.1
joblib==1.3.2
json-tricks==3.17.3
keras==2.11.0
kiwisolver==1.4.5
lazy_loader==0.3
libclang==16.0.6
librosa==0.7.1
lit==18.1.8
llvmlite==0.41.0
lpips==0.1.4
lxml==4.9.3
Markdown==3.5.1
markdown-it-py==3.0.0
MarkupSafe==2.1.3
matplotlib==3.7.3
mdurl==0.1.2
moviepy==1.0.3
mpmath==1.3.0
msgpack==1.0.7
natsort==8.4.0
networkx==3.1
numba==0.58.0
numpy==1.24.3
nvidia-cublas-cu11==**********
nvidia-cublas-cu12==********
nvidia-cuda-nvrtc-cu11==11.7.99
nvidia-cuda-runtime-cu11==11.7.99
nvidia-cudnn-cu11==********
nvidia-nvjitlink-cu12==12.8.61
oauthlib==3.2.2
omegaconf==2.0.5
onnx==1.17.0
onnxruntime==1.19.2
opencv-contrib-python==********
opencv-python==********
opencv-python-headless==********
opt-einsum==3.3.0
orjson==3.10.15
packaging==23.2
pandas==2.0.3
Pillow==10.0.1
platformdirs==3.11.0
pooch==1.7.0
portalocker==2.8.2
proglog==0.1.10
protobuf==3.20.2
psutil==6.1.1
pyasn1==0.5.0
pyasn1-modules==0.3.0
pycparser==2.21
pydantic==2.10.6
pydantic_core==2.27.2
pydub==0.25.1
Pygments==2.19.1
pyparsing==3.1.1
PySocks==1.7.1
pyspng==0.1.1
python-dateutil==2.8.2
python-multipart==0.0.20
python_speech_features==0.6
pytz==2023.3.post1
PyWavelets==1.4.1
PyYAML==6.0.1
qudida==0.0.4
regex==2023.10.3
requests==2.31.0
requests-oauthlib==1.3.1
resampy==0.4.2
rich==13.9.4
rotary-embedding-torch==0.3.5
rsa==4.9
ruff==0.9.6
s3transfer==0.7.0
sacrebleu==2.3.1
sacremoses==0.1.1
safetensors==0.5.2
scenedetect==0.5.1
scikit-image==0.21.0
scikit-learn==1.3.1
scipy==1.9.1
semantic-version==2.10.0
sentencepiece==0.1.99
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
soundfile==0.12.1
soupsieve==2.5
soxr==0.3.7
starlette==0.44.0
sympy==1.13.1
sync-batchnorm==0.0.1
tabulate==0.9.0
tensorboard==2.11.2
tensorboard-data-server==0.6.1
tensorboard-plugin-wit==1.8.1
tensorboardX==*******
tensorflow==2.11.1
tensorflow-estimator==2.11.0
tensorflow-io-gcs-filesystem==0.34.0
termcolor==2.3.0
threadpoolctl==3.2.0
tifffile==2023.7.10
tokenizers==0.20.3
tomlkit==0.12.0
torch==1.13.0
torchaudio==0.13.0
torchvision==0.14.0
tqdm==4.66.1
transformers==4.46.3
triton==2.0.0
typer==0.15.1
typing_extensions==4.12.2
tzdata==2023.3
urllib3==2.2.3
uvicorn==0.33.0
visualize==0.5.1
websockets==12.0
Werkzeug==3.0.1
wrapt==1.15.0
zipp==3.17.0
