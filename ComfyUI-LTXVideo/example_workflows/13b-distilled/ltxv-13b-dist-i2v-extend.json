{"id": "394ed254-7306-42a2-9ae6-aa880ce4456d", "revision": 0, "last_node_id": 1890, "last_link_id": 5424, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [722.5008544921875, 2398.3359375], "size": [317.832275390625, 213.2843780517578], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 75}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [3365]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "CLIPLoader", "pos": [339.99981689453125, 2310.293701171875], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "clip_name", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 1828, "type": "LTXVLatentUpsamplerModelLoader", "pos": [4692.08154296875, 2119.833251953125], "size": [344.3999938964844, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "upscale_model", "name": "upscale_model", "type": "COMBO", "widget": {"name": "upscale_model"}, "link": null}, {"localized_name": "spatial_upsample", "name": "spatial_upsample", "type": "BOOLEAN", "widget": {"name": "spatial_upsample"}, "link": null}, {"localized_name": "temporal_upsample", "name": "temporal_upsample", "type": "BOOLEAN", "widget": {"name": "temporal_upsample"}, "link": null}], "outputs": [{"localized_name": "UPSCALE_MODEL", "name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [5303]}], "properties": {"Node name for S&R": "LTXVLatentUpsamplerModelLoader"}, "widgets_values": ["ltxv-spatial-upscaler-0.9.7.safetensors", true, false]}, {"id": 1661, "type": "ImageResizeKJ", "pos": [6183.54443359375, 2173.679931640625], "size": [315, 238], "flags": {}, "order": 38, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 4749}, {"localized_name": "width_input", "name": "width_input", "shape": 7, "type": "INT", "link": null}, {"localized_name": "height_input", "name": "height_input", "shape": 7, "type": "INT", "link": null}, {"localized_name": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}, {"localized_name": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [4751]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [1280, 1280, "bicubic", true, 2, "center"]}, {"id": 1335, "type": "VAEDecode", "pos": [2515.01953125, 2253.************], "size": [210, 46], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 3576}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 5368}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [3578]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1699, "type": "LTXVFilmGrain", "pos": [6538.42822265625, 2173.679931640625], "size": [210, 82], "flags": {}, "order": 39, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 4751}, {"localized_name": "grain_intensity", "name": "grain_intensity", "type": "FLOAT", "widget": {"name": "grain_intensity"}, "link": null}, {"localized_name": "saturation", "name": "saturation", "type": "FLOAT", "widget": {"name": "saturation"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [4752]}], "properties": {"Node name for S&R": "LTXVFilmGrain"}, "widgets_values": [0.010000000000000002, 0.5]}, {"id": 1336, "type": "VHS_VideoCombine", "pos": [2789.545166015625, 2253.************], "size": [471.0374755859375, 624.691650390625], "flags": {}, "order": 31, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 3578}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv-base_00042.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24}}}}, {"id": 1858, "type": "Note", "pos": [1090.729736328125, 2106.790283203125], "size": [313.80950927734375, 120.61924743652344], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Framerate can be adjusted, just make sure you are using the same framerate in your VideoCombine node to see proper playback.\n\n60FPS = Lower quality, more motion\n24 FPS = Higher quality, less motion"], "color": "#432", "bgcolor": "#653"}, {"id": 1859, "type": "Note", "pos": [2134.************, 2111.12353515625], "size": [298.2620544433594, 88], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["We generate at a base resolution of 768x512.\nThe video will be upscaled later to FHD."], "color": "#432", "bgcolor": "#653"}, {"id": 1823, "type": "Note", "pos": [1779.07470703125, 2575.************], "size": [276.017578125, 171.05442810058594], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["The Advanced STG Guider dynamically controls the diffusion model's denoising process by mapping sigma values to specific configuration parameters (CFG scale, STG scale/rescale factors, and attention layer skipping patterns), enabling precise control at different denoising stages independently of step numbers while supporting additional techniques like CFG-Zero rescaling and threshold-based noise prediction zeroing."], "color": "#432", "bgcolor": "#653"}, {"id": 1860, "type": "Note", "pos": [2134.************, 2658.40869140625], "size": [300, 130], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["crop decides if the conditioning image(s) is center-cropped or resized by stretching \n\ncrf and blur are preprocessing parameters for conditioning image(s) to make them look more like video frames, which helps with the dynamicity of the generated video"], "color": "#432", "bgcolor": "#653"}, {"id": 1861, "type": "Note", "pos": [1510, 2368.19873046875], "size": [216.66220092773438, 88], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Presets override the values inside 🅛🅣🅧 STG Guider Advanced node."], "color": "#432", "bgcolor": "#653"}, {"id": 1825, "type": "Note", "pos": [444.50042724609375, 2121.************], "size": [232.79998779296875, 88], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Best use descriptive prompt, but you can even leave it empty in i2v case."], "color": "#432", "bgcolor": "#653"}, {"id": 1691, "type": "LTXVLatentUpsampler", "pos": [4801.28125, 2288.74267578125], "size": [235.1999969482422, 66], "flags": {}, "order": 33, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 5364}, {"localized_name": "upscale_model", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 5303}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 5287}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "links": [5297]}], "properties": {"Node name for S&R": "LTXVLatentUpsampler"}, "widgets_values": []}, {"id": 1593, "type": "LTXVAdainLatent", "pos": [4826.4814453125, 2419.431884765625], "size": [210, 78], "flags": {}, "order": 35, "mode": 0, "inputs": [{"localized_name": "latents", "name": "latents", "type": "LATENT", "link": 5297}, {"localized_name": "reference", "name": "reference", "type": "LATENT", "link": 5365}, {"localized_name": "factor", "name": "factor", "type": "FLOAT", "widget": {"name": "factor"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "links": [5422]}], "properties": {"Node name for S&R": "LTXVAdainLatent"}, "widgets_values": [0.25]}, {"id": 1863, "type": "Note", "pos": [4714.10546875, 2552.45849609375], "size": [322.3760986328125, 88], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Depending on the latent upscaler model used, it can increase either spatial or temporal resolution, without moving to/from pixel space. "], "color": "#432", "bgcolor": "#653"}, {"id": 1862, "type": "Note", "pos": [5547.77685546875, 2709.507080078125], "size": [335.1761474609375, 148.08804321289062], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["If you are not constrained in VRAM, run 1x1 tile starting from step 18 \nIf you are constrained in your available VRAM, run 2x2 tiles and use somewhat lower noies levels, starting from step 23.\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 1599, "type": "VHS_VideoCombine", "pos": [6778.427734375, 2173.679931640625], "size": [610.8043212890625, 719.1004028320312], "flags": {}, "order": 40, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 4752}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "2c25b8b53835aaeb63f831b3137c705cf9f85dce"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-hd", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 18, "save_metadata": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": true, "params": {"filename": "ltxv-hd_00004.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24, "workflow": "ltxv_00055.png", "fullpath": "C:\\Users\\<USER>\\Projects\\ComfyUI\\temp\\ltxv_00055.mp4"}, "muted": true}}}, {"id": 1865, "type": "VAEDecode", "pos": [3763.29150390625, 2248.952880859375], "size": [210, 46], "flags": {}, "order": 32, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 5355}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 5370}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [5356]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1868, "type": "RandomNoise", "pos": [3500, 2120], "size": [210, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"localized_name": "NOISE", "name": "NOISE", "type": "NOISE", "links": [5357]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "RandomNoise"}, "widgets_values": [2840, "fixed"]}, {"id": 1870, "type": "Note", "pos": [3372.0966796875, 2521.112060546875], "size": [300, 130], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["<PERSON><PERSON> decides how many of the last frames in the input video are used to condition the new video generation. The total number of frames generated is frame_overlap + num_new_frames, and then the new video and the previous video are fused by this node and the full extended video is returned."], "color": "#432", "bgcolor": "#653"}, {"id": 1872, "type": "Set VAE Decoder Noise", "pos": [2513.900390625, 2383.17529296875], "size": [235.1999969482422, 130], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "vae", "name": "vae", "type": "VAE", "link": 5367}, {"localized_name": "timestep", "name": "timestep", "type": "FLOAT", "widget": {"name": "timestep"}, "link": null}, {"localized_name": "scale", "name": "scale", "type": "FLOAT", "widget": {"name": "scale"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [5368]}], "properties": {"Node name for S&R": "Set VAE Decoder Noise"}, "widgets_values": [0.05, 0.025, 42, "fixed"]}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [1076.880615234375, 2416.77880859375], "size": [334.6303405761719, 98], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [5236, 5240, 5358, 5407, 5416]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [5243, 5287, 5359, 5367, 5369, 5370, 5417]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ltxv-13b-0.9.7-distilled.safetensors"]}, {"id": 1876, "type": "FloatToSigmas", "pos": [2223.5966796875, 2857.569091796875], "size": [216.59999084472656, 26], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "float_list", "name": "float_list", "type": "FLOAT", "link": 5379}], "outputs": [{"localized_name": "SIGMAS", "name": "SIGMAS", "type": "SIGMAS", "links": [5380, 5382]}], "properties": {"Node name for S&R": "FloatToSigmas"}, "widgets_values": []}, {"id": 73, "type": "KSamplerSelect", "pos": [1510, 2608.19873046875], "size": [251.94110107421875, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"localized_name": "SAMPLER", "name": "SAMPLER", "type": "SAMPLER", "links": [5224, 5362, 5419]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler_ancestral"]}, {"id": 1866, "type": "VHS_VideoCombine", "pos": [4038.7578125, 2250.240234375], "size": [553.4302368164062, 679.6201782226562], "flags": {}, "order": 34, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 5356}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-extend", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv-extend_00016.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24}}}}, {"id": 1867, "type": "LTXVExtendSampler", "pos": [3360, 2260], "size": [305.9637451171875, 202], "flags": {}, "order": 30, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 5358}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 5359}, {"localized_name": "latents", "name": "latents", "type": "LATENT", "link": 5360}, {"localized_name": "guider", "name": "guider", "type": "GUIDER", "link": 5413}, {"localized_name": "sampler", "name": "sampler", "type": "SAMPLER", "link": 5362}, {"localized_name": "sigmas", "name": "sigmas", "type": "SIGMAS", "link": 5382}, {"localized_name": "noise", "name": "noise", "type": "NOISE", "link": 5357}, {"localized_name": "num_new_frames", "name": "num_new_frames", "type": "INT", "widget": {"name": "num_new_frames"}, "link": null}, {"localized_name": "frame_overlap", "name": "frame_overlap", "type": "INT", "widget": {"name": "frame_overlap"}, "link": null}], "outputs": [{"localized_name": "denoised_output", "name": "denoised_output", "type": "LATENT", "links": [5355, 5364, 5365]}], "properties": {"Node name for S&R": "LTXVExtendSampler"}, "widgets_values": [120, 24]}, {"id": 1241, "type": "LTXVConditioning", "pos": [1082.************, 2271.91064453125], "size": [328.21636962890625, 78], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 3364}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 3365}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [5274, 5406]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [5275, 5405]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LTXVConditioning"}, "widgets_values": [24.000000000000004]}, {"id": 6, "type": "CLIPTextEncode", "pos": [717.5663452148438, 2114.04638671875], "size": [307.2346496582031, 204.2556610107422], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 74}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [3364]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["gorilla enters the scene and hugs the man"], "color": "#232", "bgcolor": "#353"}, {"id": 1874, "type": "Note", "pos": [1488.************, 2789.************], "size": [303.5919494628906, 88], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Full list of sigmas for the distilled model:\n1.0000, 0.9937, 0.9875, 0.9812, 0.9750, 0.9094, 0.7250, 0.4219, 0.0"], "color": "#432", "bgcolor": "#653"}, {"id": 1887, "type": "STGGuiderAdvanced", "pos": [1798.5238037109375, 2258.************], "size": [278.79998779296875, 262], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 5407}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 5406}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 5405}, {"localized_name": "preset", "name": "preset", "shape": 7, "type": "STG_ADVANCED_PRESET", "link": null}, {"localized_name": "skip_steps_sigma_threshold", "name": "skip_steps_sigma_threshold", "type": "FLOAT", "widget": {"name": "skip_steps_sigma_threshold"}, "link": null}, {"localized_name": "cfg_star_rescale", "name": "cfg_star_rescale", "type": "BOOLEAN", "widget": {"name": "cfg_star_rescale"}, "link": null}, {"localized_name": "sigmas", "name": "sigmas", "type": "STRING", "widget": {"name": "sigmas"}, "link": null}, {"localized_name": "cfg_values", "name": "cfg_values", "type": "STRING", "widget": {"name": "cfg_values"}, "link": null}, {"localized_name": "stg_scale_values", "name": "stg_scale_values", "type": "STRING", "widget": {"name": "stg_scale_values"}, "link": null}, {"localized_name": "stg_rescale_values", "name": "stg_rescale_values", "type": "STRING", "widget": {"name": "stg_rescale_values"}, "link": null}, {"localized_name": "stg_layers_indices", "name": "stg_layers_indices", "type": "STRING", "widget": {"name": "stg_layers_indices"}, "link": null}], "outputs": [{"localized_name": "GUIDER", "name": "GUIDER", "type": "GUIDER", "links": [5412, 5413]}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.9970000000000002, true, "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "1,1,1,1,1,1", "0,0,0,0,0,0", "1, 1, 1, 1, 1, 1", "[25], [35], [35], [42], [42], [42]"]}, {"id": 1338, "type": "LTXVBaseSampler", "pos": [2134.************, 2253.************], "size": [312.3999938964844, 346], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 5236}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 5243}, {"localized_name": "guider", "name": "guider", "type": "GUIDER", "link": 5412}, {"localized_name": "sampler", "name": "sampler", "type": "SAMPLER", "link": 5224}, {"localized_name": "sigmas", "name": "sigmas", "type": "SIGMAS", "link": 5380}, {"localized_name": "noise", "name": "noise", "type": "NOISE", "link": 4053}, {"localized_name": "optional_cond_images", "name": "optional_cond_images", "shape": 7, "type": "IMAGE", "link": 5162}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": null}, {"localized_name": "optional_cond_indices", "name": "optional_cond_indices", "shape": 7, "type": "STRING", "widget": {"name": "optional_cond_indices"}, "link": null}, {"localized_name": "strength", "name": "strength", "shape": 7, "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}, "link": null}, {"localized_name": "crf", "name": "crf", "shape": 7, "type": "INT", "widget": {"name": "crf"}, "link": null}, {"localized_name": "blur", "name": "blur", "shape": 7, "type": "INT", "widget": {"name": "blur"}, "link": null}], "outputs": [{"localized_name": "denoised_output", "name": "denoised_output", "type": "LATENT", "links": [3576, 5360]}], "properties": {"Node name for S&R": "LTXVBaseSampler"}, "widgets_values": [768, 512, 97, "0", 0.8, "center", 30, 1]}, {"id": 1507, "type": "RandomNoise", "pos": [1847.************, 2114.199462890625], "size": [210, 82], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"localized_name": "NOISE", "name": "NOISE", "type": "NOISE", "slot_index": 0, "links": [4053]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "RandomNoise"}, "widgets_values": [118, "fixed"]}, {"id": 1813, "type": "STGGuiderAdvanced", "pos": [5131.06298828125, 2266.************], "size": [278.79998779296875, 262], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 5240}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 5274}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 5275}, {"localized_name": "preset", "name": "preset", "shape": 7, "type": "STG_ADVANCED_PRESET", "link": null}, {"localized_name": "skip_steps_sigma_threshold", "name": "skip_steps_sigma_threshold", "type": "FLOAT", "widget": {"name": "skip_steps_sigma_threshold"}, "link": null}, {"localized_name": "cfg_star_rescale", "name": "cfg_star_rescale", "type": "BOOLEAN", "widget": {"name": "cfg_star_rescale"}, "link": null}, {"localized_name": "sigmas", "name": "sigmas", "type": "STRING", "widget": {"name": "sigmas"}, "link": null}, {"localized_name": "cfg_values", "name": "cfg_values", "type": "STRING", "widget": {"name": "cfg_values"}, "link": null}, {"localized_name": "stg_scale_values", "name": "stg_scale_values", "type": "STRING", "widget": {"name": "stg_scale_values"}, "link": null}, {"localized_name": "stg_rescale_values", "name": "stg_rescale_values", "type": "STRING", "widget": {"name": "stg_rescale_values"}, "link": null}, {"localized_name": "stg_layers_indices", "name": "stg_layers_indices", "type": "STRING", "widget": {"name": "stg_layers_indices"}, "link": null}], "outputs": [{"localized_name": "GUIDER", "name": "GUIDER", "type": "GUIDER", "links": [5421]}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.9970000000000002, true, "1", "1", "0", "1", "[42]"]}, {"id": 1875, "type": "StringToFloatList", "pos": [1864.56298828125, 2803.1142578125], "size": [210, 88], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "string", "name": "string", "type": "STRING", "widget": {"name": "string"}, "link": null}], "outputs": [{"localized_name": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [5379]}], "properties": {"Node name for S&R": "StringToFloatList", "cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350"}, "widgets_values": ["1.0000, 0.9937, 0.9875, 0.9812, 0.9750, 0.9094, 0.7250, 0.4219, 0.0"], "color": "#223", "bgcolor": "#335"}, {"id": 1889, "type": "FloatToSigmas", "pos": [5561.4443359375, 2596.************], "size": [216.59999084472656, 26], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "float_list", "name": "float_list", "type": "FLOAT", "link": 5414}], "outputs": [{"localized_name": "SIGMAS", "name": "SIGMAS", "type": "SIGMAS", "links": [5420]}], "properties": {"Node name for S&R": "FloatToSigmas"}}, {"id": 1888, "type": "StringToFloatList", "pos": [5132.404296875, 2599.130859375], "size": [395.74224853515625, 88], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "string", "name": "string", "type": "STRING", "widget": {"name": "string"}, "link": null}], "outputs": [{"localized_name": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [5414]}], "properties": {"Node name for S&R": "StringToFloatList", "cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350"}, "widgets_values": ["0.85, 0.7250, 0.6, 0.4219, 0.0"], "color": "#223", "bgcolor": "#335"}, {"id": 1598, "type": "RandomNoise", "pos": [5197.30615234375, 2125.298095703125], "size": [210, 82], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"localized_name": "NOISE", "name": "NOISE", "type": "NOISE", "links": [5418]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "RandomNoise"}, "widgets_values": [1252, "fixed"]}, {"id": 1890, "type": "LTXVTiledSampler", "pos": [5541.92041015625, 2140.************], "size": [329.20001220703125, 366], "flags": {}, "order": 36, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 5416}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 5417}, {"localized_name": "noise", "name": "noise", "type": "NOISE", "link": 5418}, {"localized_name": "sampler", "name": "sampler", "type": "SAMPLER", "link": 5419}, {"localized_name": "sigmas", "name": "sigmas", "type": "SIGMAS", "link": 5420}, {"localized_name": "guider", "name": "guider", "type": "GUIDER", "link": 5421}, {"localized_name": "latents", "name": "latents", "type": "LATENT", "link": 5422}, {"localized_name": "optional_cond_images", "name": "optional_cond_images", "shape": 7, "type": "IMAGE", "link": 5424}, {"localized_name": "horizontal_tiles", "name": "horizontal_tiles", "type": "INT", "widget": {"name": "horizontal_tiles"}, "link": null}, {"localized_name": "vertical_tiles", "name": "vertical_tiles", "type": "INT", "widget": {"name": "vertical_tiles"}, "link": null}, {"localized_name": "overlap", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "latents_cond_strength", "name": "latents_cond_strength", "type": "FLOAT", "widget": {"name": "latents_cond_strength"}, "link": null}, {"localized_name": "boost_latent_similarity", "name": "boost_latent_similarity", "type": "BOOLEAN", "widget": {"name": "boost_latent_similarity"}, "link": null}, {"localized_name": "crop", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}, {"localized_name": "optional_cond_indices", "name": "optional_cond_indices", "shape": 7, "type": "STRING", "widget": {"name": "optional_cond_indices"}, "link": null}, {"localized_name": "images_cond_strengths", "name": "images_cond_strengths", "shape": 7, "type": "STRING", "widget": {"name": "images_cond_strengths"}, "link": null}], "outputs": [{"localized_name": "output", "name": "output", "type": "LATENT", "links": [5423]}, {"localized_name": "denoised_output", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "LTXVTiledSampler"}, "widgets_values": [1, 1, 1, 0.15, false, "center", "0", "0.9"]}, {"id": 1206, "type": "LoadImage", "pos": [1069.734375, 2577.37060546875], "size": [342.5999755859375, 314], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "choose file to upload", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [5162, 5424]}, {"localized_name": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "LoadImage"}, "widgets_values": ["bravoski_lonely_warrior._Scenic_background_peace._--v_6.1_54cc19b8-1dd7-4df7-b74e-e2585b9c4458_0.png", "image"]}, {"id": 1601, "type": "VAEDecodeTiled", "pos": [5933.66064453125, 2173.679931640625], "size": [210, 150], "flags": {}, "order": 37, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 5423}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 5369}, {"localized_name": "tile_size", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "overlap", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "temporal_size", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "temporal_overlap", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [4749]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [1280, 128, 128, 32]}], "links": [[74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [3364, 6, 0, 1241, 0, "CONDITIONING"], [3365, 7, 0, 1241, 1, "CONDITIONING"], [3576, 1338, 0, 1335, 0, "LATENT"], [3578, 1335, 0, 1336, 0, "IMAGE"], [4053, 1507, 0, 1338, 5, "NOISE"], [4749, 1601, 0, 1661, 0, "IMAGE"], [4751, 1661, 0, 1699, 0, "IMAGE"], [4752, 1699, 0, 1599, 0, "IMAGE"], [5162, 1206, 0, 1338, 6, "IMAGE"], [5224, 73, 0, 1338, 3, "SAMPLER"], [5236, 44, 0, 1338, 0, "MODEL"], [5240, 44, 0, 1813, 0, "MODEL"], [5243, 44, 2, 1338, 1, "VAE"], [5274, 1241, 0, 1813, 1, "CONDITIONING"], [5275, 1241, 1, 1813, 2, "CONDITIONING"], [5287, 44, 2, 1691, 2, "VAE"], [5297, 1691, 0, 1593, 0, "LATENT"], [5303, 1828, 0, 1691, 1, "UPSCALE_MODEL"], [5355, 1867, 0, 1865, 0, "LATENT"], [5356, 1865, 0, 1866, 0, "IMAGE"], [5357, 1868, 0, 1867, 6, "NOISE"], [5358, 44, 0, 1867, 0, "MODEL"], [5359, 44, 2, 1867, 1, "VAE"], [5360, 1338, 0, 1867, 2, "LATENT"], [5362, 73, 0, 1867, 4, "SAMPLER"], [5364, 1867, 0, 1691, 0, "LATENT"], [5365, 1867, 0, 1593, 1, "LATENT"], [5367, 44, 2, 1872, 0, "VAE"], [5368, 1872, 0, 1335, 1, "VAE"], [5369, 44, 2, 1601, 1, "VAE"], [5370, 44, 2, 1865, 1, "VAE"], [5379, 1875, 0, 1876, 0, "FLOAT"], [5380, 1876, 0, 1338, 4, "SIGMAS"], [5382, 1876, 0, 1867, 5, "SIGMAS"], [5405, 1241, 1, 1887, 2, "CONDITIONING"], [5406, 1241, 0, 1887, 1, "CONDITIONING"], [5407, 44, 0, 1887, 0, "MODEL"], [5412, 1887, 0, 1338, 2, "GUIDER"], [5413, 1887, 0, 1867, 3, "GUIDER"], [5414, 1888, 0, 1889, 0, "FLOAT"], [5416, 44, 0, 1890, 0, "MODEL"], [5417, 44, 2, 1890, 1, "VAE"], [5418, 1598, 0, 1890, 2, "NOISE"], [5419, 73, 0, 1890, 3, "SAMPLER"], [5420, 1889, 0, 1890, 4, "SIGMAS"], [5421, 1813, 0, 1890, 5, "GUIDER"], [5422, 1593, 0, 1890, 6, "LATENT"], [5423, 1890, 0, 1601, 0, "LATENT"], [5424, 1206, 0, 1890, 7, "IMAGE"]], "groups": [{"id": 20, "title": "Base Low Res Generation ", "bounding": [1500, 2037.5235595703125, 1770.58251953125, 850.8944091796875], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1335, 1336, 1859, 1823, 1860, 1861, 1872, 1876, 73, 1874, 1887, 1338, 1507, 1875]}, {"id": 34, "title": "Latent Upscaler", "bounding": [4682.08154296875, 2046.2330322265625, 364.39990234375, 604.2252197265625], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1828, 1691, 1593, 1863]}, {"id": 41, "title": "Setup", "bounding": [329.9997863769531, 2040.44580078125, 1092.333984375, 860.9241943359375], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [7, 38, 1858, 1825, 44, 1241, 6, 1206]}, {"id": 42, "title": "Add Details", "bounding": [5121.06298828125, 2051.************, 2278.168701171875, 851.0821533203125], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1661, 1699, 1862, 1599, 1813, 1889, 1888, 1598, 1890, 1601]}, {"id": 43, "title": "Extend", "bounding": [3340.832275390625, 2042.8255615234375, 1261.35595703125, 897.0347900390625], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1865, 1868, 1870, 1866, 1867]}], "config": {}, "extra": {"ds": {"scale": 0.3504938994813925, "offset": [155.50582134587447, -906.0461897551281]}, "frontendVersion": "1.17.11", "VHS_latentpreview": true, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "prompt": {"6": {"inputs": {"text": "gorilla enters the scene and hugs the man", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "38": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "44": {"inputs": {"ckpt_name": "ltxv-13b-0.9.7-distilled.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "73": {"inputs": {"sampler_name": "euler_ancestral"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "1206": {"inputs": {"image": "bravoski_lonely_warrior._Scenic_background_peace._--v_6.1_54cc19b8-1dd7-4df7-b74e-e2585b9c4458_0.png"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1241": {"inputs": {"frame_rate": 24.000000000000004, "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "1335": {"inputs": {"samples": ["1338", 0], "vae": ["1872", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1336": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "images": ["1335", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1338": {"inputs": {"width": 768, "height": 512, "num_frames": 97, "optional_cond_indices": "0", "strength": 0.8, "crop": "center", "crf": 30, "blur": 1, "model": ["44", 0], "vae": ["44", 2], "guider": ["1887", 0], "sampler": ["73", 0], "sigmas": ["1876", 0], "noise": ["1507", 0], "optional_cond_images": ["1206", 0]}, "class_type": "LTXVBaseSampler", "_meta": {"title": "🅛🅣🅧 LTXV Base Sampler"}}, "1507": {"inputs": {"noise_seed": 118}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1593": {"inputs": {"factor": 0.25, "latents": ["1691", 0], "reference": ["1867", 0]}, "class_type": "LTXVAdainLatent", "_meta": {"title": "🅛🅣🅧 LTXV Adain Latent"}}, "1598": {"inputs": {"noise_seed": 1252}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1599": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-hd", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 18, "save_metadata": false, "pingpong": false, "save_output": false, "images": ["1699", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1601": {"inputs": {"tile_size": 1280, "overlap": 128, "temporal_size": 128, "temporal_overlap": 32, "samples": ["1890", 0], "vae": ["44", 2]}, "class_type": "VAEDecodeTiled", "_meta": {"title": "VAE Decode (Tiled)"}}, "1661": {"inputs": {"width": 1280, "height": 1280, "upscale_method": "bicubic", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["1601", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "1691": {"inputs": {"samples": ["1867", 0], "upscale_model": ["1828", 0], "vae": ["44", 2]}, "class_type": "LTXVLatentUpsampler", "_meta": {"title": "🅛🅣🅧 LTXV Latent Upsampler"}}, "1699": {"inputs": {"grain_intensity": 0.010000000000000002, "saturation": 0.5, "images": ["1661", 0]}, "class_type": "LTXVFilmGrain", "_meta": {"title": "🅛🅣🅧 LTXV Film Grain"}}, "1813": {"inputs": {"skip_steps_sigma_threshold": 0.9970000000000002, "cfg_star_rescale": true, "sigmas": "1", "cfg_values": "1", "stg_scale_values": "0", "stg_rescale_values": "1", "stg_layers_indices": "[42]", "model": ["44", 0], "positive": ["1241", 0], "negative": ["1241", 1]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "1828": {"inputs": {"upscale_model": "ltxv-spatial-upscaler-0.9.7.safetensors", "spatial_upsample": true, "temporal_upsample": false}, "class_type": "LTXVLatentUpsamplerModelLoader", "_meta": {"title": "🅛🅣🅧 LTXV Latent Upsampler Model Loader"}}, "1865": {"inputs": {"samples": ["1867", 0], "vae": ["44", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1866": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-extend", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "images": ["1865", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1867": {"inputs": {"num_new_frames": 120, "frame_overlap": 24, "model": ["44", 0], "vae": ["44", 2], "latents": ["1338", 0], "guider": ["1887", 0], "sampler": ["73", 0], "sigmas": ["1876", 0], "noise": ["1868", 0]}, "class_type": "LTXVExtendSampler", "_meta": {"title": "🅛🅣🅧 LTXV Extend Sampler"}}, "1868": {"inputs": {"noise_seed": 2840}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1872": {"inputs": {"timestep": 0.05, "scale": 0.025, "seed": 42, "vae": ["44", 2]}, "class_type": "Set VAE Decoder Noise", "_meta": {"title": "🅛🅣🅧 Set VAE Decoder Noise"}}, "1875": {"inputs": {"string": "1.0000, 0.9937, 0.9875, 0.9812, 0.9750, 0.9094, 0.7250, 0.4219, 0.0"}, "class_type": "StringToFloatList", "_meta": {"title": "String to Float List"}}, "1876": {"inputs": {"float_list": ["1875", 0]}, "class_type": "FloatToSigmas", "_meta": {"title": "Float To Sigmas"}}, "1887": {"inputs": {"skip_steps_sigma_threshold": 0.9970000000000002, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "1,1,1,1,1,1", "stg_scale_values": "0,0,0,0,0,0", "stg_rescale_values": "1, 1, 1, 1, 1, 1", "stg_layers_indices": "[25], [35], [35], [42], [42], [42]", "model": ["44", 0], "positive": ["1241", 0], "negative": ["1241", 1]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "1888": {"inputs": {"string": "0.85, 0.7250, 0.6, 0.4219, 0.0"}, "class_type": "StringToFloatList", "_meta": {"title": "String to Float List"}}, "1889": {"inputs": {"float_list": ["1888", 0]}, "class_type": "FloatToSigmas", "_meta": {"title": "Float To Sigmas"}}, "1890": {"inputs": {"horizontal_tiles": 1, "vertical_tiles": 1, "overlap": 1, "latents_cond_strength": 0.15, "boost_latent_similarity": false, "crop": "center", "optional_cond_indices": "0", "images_cond_strengths": "0.9", "model": ["44", 0], "vae": ["44", 2], "noise": ["1598", 0], "sampler": ["73", 0], "sigmas": ["1889", 0], "guider": ["1813", 0], "latents": ["1593", 0], "optional_cond_images": ["1206", 0]}, "class_type": "LTXVTiledSampler", "_meta": {"title": "🅛🅣🅧 LTXV Tiled Sampler"}}}, "comfy_fork_version": "develop@580b3007", "workspace_info": {"id": "elBQFQknIoLYTEwIloQuw"}, "node_versions": {"comfy-core": "0.3.20"}}, "version": 0.4}