{"id": "394ed254-7306-42a2-9ae6-aa880ce4456d", "revision": 0, "last_node_id": 1871, "last_link_id": 5372, "nodes": [{"id": 1601, "type": "VAEDecodeTiled", "pos": [4565.6015625, 2168.989501953125], "size": [210, 150], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 5372}, {"name": "vae", "type": "VAE", "link": 5362}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4749]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [1280, 128, 256, 32]}, {"id": 1241, "type": "LTXVConditioning", "pos": [1090.674072265625, 2272.830322265625], "size": [328.21636962890625, 78], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 3364}, {"name": "negative", "type": "CONDITIONING", "link": 3365}], "outputs": [{"name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [5227, 5274]}, {"name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [5228, 5275]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LTXVConditioning"}, "widgets_values": [24.000000000000004]}, {"id": 7, "type": "CLIPTextEncode", "pos": [730.2447509765625, 2399.255615234375], "size": [317.832275390625, 213.2843780517578], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [3365]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "CLIPLoader", "pos": [347.74371337890625, 2311.21337890625], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 1828, "type": "LTXVLatentUpsamplerModelLoader", "pos": [3338.0751953125, 2116.379638671875], "size": [344.3999938964844, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [5303]}], "properties": {"Node name for S&R": "LTXVLatentUpsamplerModelLoader"}, "widgets_values": ["ltxv-spatial-upscaler-0.9.7.safetensors", true, false]}, {"id": 1661, "type": "ImageResizeKJ", "pos": [4825.6025390625, 2168.989501953125], "size": [315, 238], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 4749}, {"name": "width_input", "shape": 7, "type": "INT", "link": null}, {"name": "height_input", "shape": 7, "type": "INT", "link": null}, {"name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4751]}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [1280, 1280, "bicubic", true, 2, "center"]}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [1084.624267578125, 2417.698486328125], "size": [334.6303405761719, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [2951, 5236, 5239, 5240, 5364]}, {"name": "CLIP", "type": "CLIP", "links": []}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [5243, 5287, 5360, 5365]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ltxv-13b-0.9.7-dev.safetensors"]}, {"id": 1335, "type": "VAEDecode", "pos": [2515.01953125, 2253.************], "size": [210, 46], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 3576}, {"name": "vae", "type": "VAE", "link": 5361}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [3578]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1699, "type": "LTXVFilmGrain", "pos": [5175.60205078125, 2168.989501953125], "size": [210, 82], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 4751}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4752]}], "properties": {"Node name for S&R": "LTXVFilmGrain"}, "widgets_values": [0.010000000000000002, 0.5]}, {"id": 1336, "type": "VHS_VideoCombine", "pos": [2789.545166015625, 2253.************], "size": [471.0374755859375, 310], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "images", "shape": 7, "type": "IMAGE", "link": 3578}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv-base_00002.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24}}}}, {"id": 6, "type": "CLIPTextEncode", "pos": [725.3102416992188, 2114.966064453125], "size": [307.2346496582031, 204.2556610107422], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [3364]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#232", "bgcolor": "#353"}, {"id": 1507, "type": "RandomNoise", "pos": [1847.874755859375, 2114.199462890625], "size": [210, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "slot_index": 0, "links": [4053]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "RandomNoise"}, "widgets_values": [108, "fixed"]}, {"id": 1858, "type": "Note", "pos": [1098.473388671875, 2107.7099609375], "size": [313.80950927734375, 120.61924743652344], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Framerate can be adjusted, just make sure you are using the same framerate in your VideoCombine node to see proper playback.\n\n60FPS = Lower quality, more motion\n24 FPS = Higher quality, less motion"], "color": "#432", "bgcolor": "#653"}, {"id": 1859, "type": "Note", "pos": [2134.************, 2111.12353515625], "size": [298.2620544433594, 88], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["We generate at a base resolution of 768x512.\nThe video will be upscaled later to FHD."], "color": "#432", "bgcolor": "#653"}, {"id": 1823, "type": "Note", "pos": [1779.07470703125, 2575.************], "size": [276.017578125, 171.05442810058594], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["The Advanced STG Guider dynamically controls the diffusion model's denoising process by mapping sigma values to specific configuration parameters (CFG scale, STG scale/rescale factors, and attention layer skipping patterns), enabling precise control at different denoising stages independently of step numbers while supporting additional techniques like CFG-Zero rescaling and threshold-based noise prediction zeroing."], "color": "#432", "bgcolor": "#653"}, {"id": 1807, "type": "STGGuiderAdvanced", "pos": [1779.07470703125, 2253.************], "size": [278.79998779296875, 262], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5239}, {"name": "positive", "type": "CONDITIONING", "link": 5227}, {"name": "negative", "type": "CONDITIONING", "link": 5228}, {"name": "preset", "shape": 7, "type": "STG_ADVANCED_PRESET", "link": 5351}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [5167]}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.9970000000000002, true, "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "1,16,8,8,4,1", "0, 4, 4, 2, 1, 1", "1, 1, 1, 1, 1, 1", "[35], [35], [35], [42], [42], [42]"]}, {"id": 1861, "type": "Note", "pos": [1510, 2368.19873046875], "size": [216.66220092773438, 88], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Presets override the values inside 🅛🅣🅧 STG Guider Advanced node."], "color": "#432", "bgcolor": "#653"}, {"id": 1103, "type": "BasicScheduler", "pos": [1510, 2718.19873046875], "size": [245.24002075195312, 106], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2951}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [5225, 5258]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["linear_quadratic", 30, 1]}, {"id": 73, "type": "KSamplerSelect", "pos": [1510, 2608.19873046875], "size": [251.94110107421875, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [5224, 5367]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["gradient_estimation"]}, {"id": 1809, "type": "STGAdvancedPresets", "pos": [1510, 2258.19873046875], "size": [226.8000030517578, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "STG_ADVANCED_PRESET", "type": "STG_ADVANCED_PRESET", "links": [5351]}], "properties": {"Node name for S&R": "STGAdvancedPresets"}, "widgets_values": ["13b Dynamic"]}, {"id": 1825, "type": "Note", "pos": [452.24432373046875, 2122.3583984375], "size": [232.79998779296875, 88], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Best use descriptive prompt, but you can even leave it empty in i2v case."], "color": "#432", "bgcolor": "#653"}, {"id": 1691, "type": "LTXVLatentUpsampler", "pos": [3447.275146484375, 2285.2890625], "size": [235.1999969482422, 66], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 5295}, {"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 5303}, {"name": "vae", "type": "VAE", "link": 5287}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [5297]}], "properties": {"Node name for S&R": "LTXVLatentUpsampler"}, "widgets_values": []}, {"id": 1593, "type": "LTXVAdainLatent", "pos": [3472.47509765625, 2415.978271484375], "size": [210, 78], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "latents", "type": "LATENT", "link": 5297}, {"name": "reference", "type": "LATENT", "link": 5302}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [5370]}], "properties": {"Node name for S&R": "LTXVAdainLatent"}, "widgets_values": [0.25]}, {"id": 1863, "type": "Note", "pos": [3360.09912109375, 2549.0048828125], "size": [322.3760986328125, 88], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Depending on the latent upscaler model used, it can increase either spatial or temporal resolution, without moving to/from pixel space. "], "color": "#432", "bgcolor": "#653"}, {"id": 1598, "type": "RandomNoise", "pos": [3834.480224609375, 2120.607666015625], "size": [210, 82], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [5366]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "RandomNoise"}, "widgets_values": [191, "fixed"]}, {"id": 1862, "type": "Note", "pos": [4184.95068359375, 2704.816650390625], "size": [335.1761474609375, 148.08804321289062], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["If you are not constrained in VRAM, run 1x1 tile starting from step 18 \nIf you are constrained in your available VRAM, run 2x2 tiles and use somewhat lower noies levels, starting from step 23.\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 1599, "type": "VHS_VideoCombine", "pos": [5415.6015625, 2168.989501953125], "size": [610.8043212890625, 310], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "images", "shape": 7, "type": "IMAGE", "link": 4752}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "2c25b8b53835aaeb63f831b3137c705cf9f85dce"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-hd", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 18, "save_metadata": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": true, "params": {"filename": "ltxv-hd_00002.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24, "workflow": "ltxv_00055.png", "fullpath": "C:\\Users\\<USER>\\Projects\\ComfyUI\\temp\\ltxv_00055.mp4"}, "muted": true}}}, {"id": 1813, "type": "STGGuiderAdvanced", "pos": [3768.************, 2261.************], "size": [278.79998779296875, 262], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5240}, {"name": "positive", "type": "CONDITIONING", "link": 5274}, {"name": "negative", "type": "CONDITIONING", "link": 5275}, {"name": "preset", "shape": 7, "type": "STG_ADVANCED_PRESET", "link": 5354}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [5369]}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.9970000000000002, true, "1", "1", "1", "1", "[42]"]}, {"id": 1206, "type": "LoadImage", "pos": [347.************, 2700.************], "size": [342.5999755859375, 314], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5355, 5371]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "LoadImage"}, "widgets_values": ["2A.png", "image"]}, {"id": 1865, "type": "LoadImage", "pos": [707.************, 2700.************], "size": [342.5999755859375, 314], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5356]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "LoadImage"}, "widgets_values": ["2B.png", "image"]}, {"id": 1866, "type": "LoadImage", "pos": [1067.74365234375, 2700.************], "size": [342.5999755859375, 314], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5358]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "LoadImage"}, "widgets_values": ["2C (1).png", "image"]}, {"id": 1867, "type": "ImageBatch", "pos": [1197.74365234375, 3080.************], "size": [210, 46], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 5355}, {"name": "image2", "type": "IMAGE", "link": 5356}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5357]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 1868, "type": "ImageBatch", "pos": [1197.74365234375, 3180.************], "size": [210, 46], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 5357}, {"name": "image2", "type": "IMAGE", "link": 5358}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5359]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 1860, "type": "Note", "pos": [2134.************, 2658.40869140625], "size": [300, 130], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["crop decides if the conditioning image(s) is center-cropped or resized by stretching \n\ncrf and blur are preprocessing parameters for conditioning image(s) to make them look more like video frames, which helps with the dynamicity of the generated video"], "color": "#432", "bgcolor": "#653"}, {"id": 1338, "type": "LTXVBaseSampler", "pos": [2134.************, 2253.************], "size": [312.3999938964844, 346], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5236}, {"name": "vae", "type": "VAE", "link": 5243}, {"name": "guider", "type": "GUIDER", "link": 5167}, {"name": "sampler", "type": "SAMPLER", "link": 5224}, {"name": "sigmas", "type": "SIGMAS", "link": 5225}, {"name": "noise", "type": "NOISE", "link": 4053}, {"name": "optional_cond_images", "shape": 7, "type": "IMAGE", "link": 5359}], "outputs": [{"name": "denoised_output", "type": "LATENT", "links": [3576, 5295, 5302]}], "properties": {"Node name for S&R": "LTXVBaseSampler"}, "widgets_values": [768, 512, 97, "0, 40, 90", 0.8, "center", 30, 0]}, {"id": 1869, "type": "Note", "pos": [727.5205688476562, 3077.712890625], "size": [300, 130], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["This flow shows keyframe interpolation, where keyframes are positioned in indices 0, 40 and 90 in 🅛🅣🅧 LTXV Base Sampler."], "color": "#432", "bgcolor": "#653"}, {"id": 1864, "type": "STGAdvancedPresets", "pos": [3780.39501953125, 2595.4013671875], "size": [264.6961364746094, 58], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "STG_ADVANCED_PRESET", "type": "STG_ADVANCED_PRESET", "links": [5354]}], "properties": {"Node name for S&R": "STGAdvancedPresets"}, "widgets_values": ["13b <PERSON><PERSON><PERSON>"]}, {"id": 1870, "type": "Set VAE Decoder Noise", "pos": [2494.045654296875, 2376.8134765625], "size": [257.5447082519531, 130], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "vae", "type": "VAE", "link": 5360}], "outputs": [{"name": "VAE", "type": "VAE", "links": [5361, 5362]}], "properties": {"Node name for S&R": "Set VAE Decoder Noise"}, "widgets_values": [0.05, 0.025, 42, "randomize"]}, {"id": 1871, "type": "LTXVTiledSampler", "pos": [4180, 2170], "size": [350, 366], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5364}, {"name": "vae", "type": "VAE", "link": 5365}, {"name": "noise", "type": "NOISE", "link": 5366}, {"name": "sampler", "type": "SAMPLER", "link": 5367}, {"name": "sigmas", "type": "SIGMAS", "link": 5368}, {"name": "guider", "type": "GUIDER", "link": 5369}, {"name": "latents", "type": "LATENT", "link": 5370}, {"name": "optional_cond_images", "shape": 7, "type": "IMAGE", "link": 5371}], "outputs": [{"name": "output", "type": "LATENT", "links": [5372]}, {"name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "LTXVTiledSampler"}, "widgets_values": [1, 1, 1, 0.15, false, "disabled", "0", "0.9"]}, {"id": 1596, "type": "SplitSigmas", "pos": [4183.4150390625, 2577.************], "size": [271.1412048339844, 78], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "sigmas", "type": "SIGMAS", "link": 5258}], "outputs": [{"name": "high_sigmas", "type": "SIGMAS", "links": null}, {"name": "low_sigmas", "type": "SIGMAS", "links": [5368]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "SplitSigmas"}, "widgets_values": [18]}], "links": [[74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [2951, 44, 0, 1103, 0, "MODEL"], [3364, 6, 0, 1241, 0, "CONDITIONING"], [3365, 7, 0, 1241, 1, "CONDITIONING"], [3576, 1338, 0, 1335, 0, "LATENT"], [3578, 1335, 0, 1336, 0, "IMAGE"], [4053, 1507, 0, 1338, 5, "NOISE"], [4749, 1601, 0, 1661, 0, "IMAGE"], [4751, 1661, 0, 1699, 0, "IMAGE"], [4752, 1699, 0, 1599, 0, "IMAGE"], [5167, 1807, 0, 1338, 2, "GUIDER"], [5224, 73, 0, 1338, 3, "SAMPLER"], [5225, 1103, 0, 1338, 4, "SIGMAS"], [5227, 1241, 0, 1807, 1, "CONDITIONING"], [5228, 1241, 1, 1807, 2, "CONDITIONING"], [5236, 44, 0, 1338, 0, "MODEL"], [5239, 44, 0, 1807, 0, "MODEL"], [5240, 44, 0, 1813, 0, "MODEL"], [5243, 44, 2, 1338, 1, "VAE"], [5258, 1103, 0, 1596, 0, "SIGMAS"], [5274, 1241, 0, 1813, 1, "CONDITIONING"], [5275, 1241, 1, 1813, 2, "CONDITIONING"], [5287, 44, 2, 1691, 2, "VAE"], [5295, 1338, 0, 1691, 0, "LATENT"], [5297, 1691, 0, 1593, 0, "LATENT"], [5302, 1338, 0, 1593, 1, "LATENT"], [5303, 1828, 0, 1691, 1, "UPSCALE_MODEL"], [5351, 1809, 0, 1807, 3, "STG_ADVANCED_PRESET"], [5354, 1864, 0, 1813, 3, "STG_ADVANCED_PRESET"], [5355, 1206, 0, 1867, 0, "IMAGE"], [5356, 1865, 0, 1867, 1, "IMAGE"], [5357, 1867, 0, 1868, 0, "IMAGE"], [5358, 1866, 0, 1868, 1, "IMAGE"], [5359, 1868, 0, 1338, 6, "IMAGE"], [5360, 44, 2, 1870, 0, "VAE"], [5361, 1870, 0, 1335, 1, "VAE"], [5362, 1870, 0, 1601, 1, "VAE"], [5364, 44, 0, 1871, 0, "MODEL"], [5365, 44, 2, 1871, 1, "VAE"], [5366, 1598, 0, 1871, 2, "NOISE"], [5367, 73, 0, 1871, 3, "SAMPLER"], [5368, 1596, 1, 1871, 4, "SIGMAS"], [5369, 1813, 0, 1871, 5, "GUIDER"], [5370, 1593, 0, 1871, 6, "LATENT"], [5371, 1206, 0, 1871, 7, "IMAGE"], [5372, 1871, 0, 1601, 0, "LATENT"]], "groups": [{"id": 20, "title": "Base Low Res Generation ", "bounding": [1500, 2040, 1770.58251953125, 850.8944091796875], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1335, 1336, 1507, 1859, 1823, 1807, 1861, 1103, 73, 1809, 1860, 1338, 1870]}, {"id": 34, "title": "Latent Upscaler", "bounding": [3330, 2040, 364.39990234375, 604.2252197265625], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1828, 1691, 1593, 1863]}, {"id": 41, "title": "Setup", "bounding": [340, 2030, 1091.51123046875, 1202.8096923828125], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1241, 7, 38, 44, 6, 1858, 1825, 1206, 1865, 1866, 1867, 1868, 1869]}, {"id": 42, "title": "Add Details", "bounding": [3760, 2050, 2278.168701171875, 851.0821533203125], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1601, 1661, 1699, 1598, 1862, 1599, 1813, 1864, 1871, 1596]}], "config": {}, "extra": {"ds": {"scale": 0.8140274938683998, "offset": [-2644.9159383871674, -1726.834069190988]}, "frontendVersion": "1.17.11", "VHS_latentpreview": true, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "prompt": {"6": {"inputs": {"text": "", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "38": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "44": {"inputs": {"ckpt_name": "ltxv-13b-0.9.7-dev.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "73": {"inputs": {"sampler_name": "gradient_estimation"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "1103": {"inputs": {"scheduler": "linear_quadratic", "steps": 30, "denoise": 1, "model": ["44", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "1206": {"inputs": {"image": "2A.png"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1241": {"inputs": {"frame_rate": 24.000000000000004, "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "1335": {"inputs": {"samples": ["1338", 0], "vae": ["1870", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1336": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "images": ["1335", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1338": {"inputs": {"width": 768, "height": 512, "num_frames": 97, "optional_cond_indices": "0, 40, 90", "strength": 0.8, "crop": "center", "crf": 30, "blur": 0, "model": ["44", 0], "vae": ["44", 2], "guider": ["1807", 0], "sampler": ["73", 0], "sigmas": ["1103", 0], "noise": ["1507", 0], "optional_cond_images": ["1868", 0]}, "class_type": "LTXVBaseSampler", "_meta": {"title": "🅛🅣🅧 LTXV Base Sampler"}}, "1507": {"inputs": {"noise_seed": 108}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1593": {"inputs": {"factor": 0.25, "latents": ["1691", 0], "reference": ["1338", 0]}, "class_type": "LTXVAdainLatent", "_meta": {"title": "🅛🅣🅧 LTXV Adain Latent"}}, "1596": {"inputs": {"step": 18, "sigmas": ["1103", 0]}, "class_type": "SplitSigmas", "_meta": {"title": "SplitSigmas"}}, "1598": {"inputs": {"noise_seed": 191}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1599": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-hd", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 18, "save_metadata": false, "pingpong": false, "save_output": false, "images": ["1699", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1601": {"inputs": {"tile_size": 1280, "overlap": 128, "temporal_size": 256, "temporal_overlap": 32, "samples": ["1796", 0], "vae": ["1870", 0]}, "class_type": "VAEDecodeTiled", "_meta": {"title": "VAE Decode (Tiled)"}}, "1661": {"inputs": {"width": 1280, "height": 1280, "upscale_method": "bicubic", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["1601", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "1691": {"inputs": {"samples": ["1338", 0], "upscale_model": ["1828", 0], "vae": ["44", 2]}, "class_type": "LTXVLatentUpsampler", "_meta": {"title": "🅛🅣🅧 LTXV Latent Upsampler"}}, "1699": {"inputs": {"grain_intensity": 0.010000000000000002, "saturation": 0.5, "images": ["1661", 0]}, "class_type": "LTXVFilmGrain", "_meta": {"title": "🅛🅣🅧 LTXV Film Grain"}}, "1796": {"inputs": {"horizontal_tiles": 1, "vertical_tiles": 1, "overlap": 3, "first_frame_cond_strength": 0.9, "latents_cond_strength": 0.15, "boost_latent_similarity": true, "crop": "center", "model": ["44", 0], "vae": ["44", 2], "noise": ["1598", 0], "sampler": ["73", 0], "sigmas": ["1596", 1], "guider": ["1813", 0], "latents": ["1593", 0], "optional_cond_image": ["1206", 0]}, "class_type": "LTXVTiledSampler", "_meta": {"title": "🅛🅣🅧 LTXV Tiled Sampler"}}, "1807": {"inputs": {"skip_steps_sigma_threshold": 0.9970000000000002, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "1,16,8,8,4,1", "stg_scale_values": "0, 4, 4, 2, 1, 1", "stg_rescale_values": "1, 1, 1, 1, 1, 1", "stg_layers_indices": "[35], [35], [35], [42], [42], [42]", "model": ["44", 0], "positive": ["1241", 0], "negative": ["1241", 1], "preset": ["1809", 0]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "1809": {"inputs": {"preset": "13b Dynamic"}, "class_type": "STGAdvancedPresets", "_meta": {"title": "🅛🅣🅧 STG Advanced Presets"}}, "1813": {"inputs": {"skip_steps_sigma_threshold": 0.9970000000000002, "cfg_star_rescale": true, "sigmas": "1", "cfg_values": "1", "stg_scale_values": "1", "stg_rescale_values": "1", "stg_layers_indices": "[42]", "model": ["44", 0], "positive": ["1241", 0], "negative": ["1241", 1], "preset": ["1864", 0]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "1828": {"inputs": {"upscale_model": "ltxv-spatial-upscaler-0.9.7.safetensors", "spatial_upsample": true, "temporal_upsample": false}, "class_type": "LTXVLatentUpsamplerModelLoader", "_meta": {"title": "🅛🅣🅧 LTXV Latent Upsampler Model Loader"}}, "1864": {"inputs": {"preset": "13b <PERSON><PERSON><PERSON>"}, "class_type": "STGAdvancedPresets", "_meta": {"title": "🅛🅣🅧 STG Advanced Presets"}}, "1865": {"inputs": {"image": "2B.png"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1866": {"inputs": {"image": "2C (1).png"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1867": {"inputs": {"image1": ["1206", 0], "image2": ["1865", 0]}, "class_type": "ImageBatch", "_meta": {"title": "Batch Images"}}, "1868": {"inputs": {"image1": ["1867", 0], "image2": ["1866", 0]}, "class_type": "ImageBatch", "_meta": {"title": "Batch Images"}}, "1870": {"inputs": {"timestep": 0.05, "scale": 0.025, "seed": 42, "vae": ["44", 2]}, "class_type": "Set VAE Decoder Noise", "_meta": {"title": "🅛🅣🅧 Set VAE Decoder Noise"}}}, "comfy_fork_version": "develop@311c9b81", "workspace_info": {"id": "elBQFQknIoLYTEwIloQuw"}, "node_versions": {"comfy-core": "0.3.30", "comfyui-kjnodes": "1.0.8"}}, "version": 0.4}