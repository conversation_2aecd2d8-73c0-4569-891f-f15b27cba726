{"id": "394ed254-7306-42a2-9ae6-aa880ce4456d", "revision": 0, "last_node_id": 1954, "last_link_id": 5574, "nodes": [{"id": 1601, "type": "VAEDecodeTiled", "pos": [5286.86767578125, 2623.205810546875], "size": [210, 150], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 5416}, {"name": "vae", "type": "VAE", "link": 5364}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4749]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [1280, 128, 256, 32]}, {"id": 38, "type": "CLIPLoader", "pos": [382.3058166503906, 2813.069091796875], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 1828, "type": "LTXVLatentUpsamplerModelLoader", "pos": [4015.9755859375, 2609.3974609375], "size": [344.3999938964844, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [5303]}], "properties": {"Node name for S&R": "LTXVLatentUpsamplerModelLoader"}, "widgets_values": ["ltxv-spatial-upscaler-0.9.7.safetensors", true, false]}, {"id": 1699, "type": "LTXVFilmGrain", "pos": [5896.8681640625, 2623.205810546875], "size": [210, 82], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 4751}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5561]}], "properties": {"Node name for S&R": "LTXVFilmGrain"}, "widgets_values": [0.010000000000000002, 0.5]}, {"id": 1336, "type": "VHS_VideoCombine", "pos": [3390, 2300], "size": [471.0374755859375, 624.************], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "images", "shape": 7, "type": "IMAGE", "link": 3578}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv-base_00034.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24}}}}, {"id": 7, "type": "CLIPTextEncode", "pos": [764.8067626953125, 2901.111328125], "size": [317.832275390625, 213.2843780517578], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [3365]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 1661, "type": "ImageResizeKJ", "pos": [5546.86865234375, 2623.205810546875], "size": [315, 238], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 4749}, {"name": "width_input", "shape": 7, "type": "INT", "link": null}, {"name": "height_input", "shape": 7, "type": "INT", "link": null}, {"name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4751]}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [1280, 1280, "bicubic", true, 2, "center"]}, {"id": 1863, "type": "Note", "pos": [4037.99951171875, 3042.022705078125], "size": [322.3760986328125, 88], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Depending on the latent upscaler model used, it can increase either spatial or temporal resolution, without moving to/from pixel space. "], "color": "#432", "bgcolor": "#653"}, {"id": 1593, "type": "LTXVAdainLatent", "pos": [4150.37646484375, 2910.26513671875], "size": [210, 78], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "latents", "type": "LATENT", "link": 5297}, {"name": "reference", "type": "LATENT", "link": 5517}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [5512]}], "properties": {"Node name for S&R": "LTXVAdainLatent"}, "widgets_values": [0.25]}, {"id": 1871, "type": "STGGuiderAdvanced", "pos": [4538.71044921875, 2728.************], "size": [278.79998779296875, 262], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5520}, {"name": "positive", "type": "CONDITIONING", "link": 5366}, {"name": "negative", "type": "CONDITIONING", "link": 5367}, {"name": "preset", "shape": 7, "type": "STG_ADVANCED_PRESET", "link": null}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [5374]}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.9970000000000002, true, "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "1,1,1,1,1,1", "0,0,0,0,0,0", "1,1,1,1,1,1", "[41],[42],[42],[42],[30],[30]"]}, {"id": 1241, "type": "LTXVConditioning", "pos": [1107.118896484375, 2774.681640625], "size": [328.21636962890625, 78], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 3364}, {"name": "negative", "type": "CONDITIONING", "link": 3365}], "outputs": [{"name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [5366, 5459, 5493]}, {"name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [5367, 5460, 5494]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LTXVConditioning"}, "widgets_values": [24.000000000000004]}, {"id": 1872, "type": "LTXVTiledSampler", "pos": [4902.1083984375, 2633.************], "size": [329.20001220703125, 366], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5513}, {"name": "vae", "type": "VAE", "link": 5370}, {"name": "noise", "type": "NOISE", "link": 5371}, {"name": "sampler", "type": "SAMPLER", "link": 5519}, {"name": "sigmas", "type": "SIGMAS", "link": 5516}, {"name": "guider", "type": "GUIDER", "link": 5374}, {"name": "latents", "type": "LATENT", "link": 5512}, {"name": "optional_cond_images", "shape": 7, "type": "IMAGE", "link": 5377}], "outputs": [{"name": "output", "type": "LATENT", "links": []}, {"name": "denoised_output", "type": "LATENT", "links": [5416]}], "properties": {"Node name for S&R": "LTXVTiledSampler"}, "widgets_values": [1, 1, 3, 0.15, false, "center", "0", "0.9"]}, {"id": 1598, "type": "RandomNoise", "pos": [4555.74609375, 2574.************], "size": [210, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [5371]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "RandomNoise"}, "widgets_values": [421, "fixed"]}, {"id": 1899, "type": "VAEDecode", "pos": [3140, 3060], "size": [210, 46], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 5482}, {"name": "vae", "type": "VAE", "link": 5439}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5440]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1691, "type": "LTXVLatentUpsampler", "pos": [4125.17626953125, 2778.306884765625], "size": [235.1999969482422, 66], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 5518}, {"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 5303}, {"name": "vae", "type": "VAE", "link": 5287}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [5297]}], "properties": {"Node name for S&R": "LTXVLatentUpsampler"}, "widgets_values": []}, {"id": 1948, "type": "Note", "pos": [1548.50048828125, 2889.4560546875], "size": [303.5919494628906, 88], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Full list of sigmas for the distilled model:\n1.0000, 0.9937, 0.9875, 0.9812, 0.9750, 0.9094, 0.7250, 0.4219, 0.0"], "color": "#432", "bgcolor": "#653"}, {"id": 1912, "type": "CFGGuider", "pos": [2399.87060546875, 3338.296875], "size": [210, 98], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5471}, {"name": "positive", "type": "CONDITIONING", "link": 5508}, {"name": "negative", "type": "CONDITIONING", "link": 5509}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [5474]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "CFGGuider"}, "widgets_values": [1]}, {"id": 1909, "type": "LTXVAddGuide", "pos": [2018.3248291015625, 3288.************], "size": [315, 162], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 5459}, {"name": "negative", "type": "CONDITIONING", "link": 5460}, {"name": "vae", "type": "VAE", "link": 5461}, {"name": "latent", "type": "LATENT", "link": 5573}, {"name": "image", "type": "IMAGE", "link": 5470}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [5464, 5508]}, {"name": "negative", "type": "CONDITIONING", "links": [5465, 5509]}, {"name": "latent", "type": "LATENT", "links": [5505]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "LTXVAddGuide"}, "widgets_values": [0, 0.9000000000000001]}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [1519.220703125, 2107.************], "size": [334.6303405761719, 98], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [5499]}, {"name": "CLIP", "type": "CLIP", "links": []}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [5287, 5362, 5370, 5461, 5495]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ltxv-13b-0.9.7-dev.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 1926, "type": "KSamplerSelect", "pos": [4928.5146484375, 3070.43408203125], "size": [251.94110107421875, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [5519]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler_ancestral"]}, {"id": 1924, "type": "FloatToSigmas", "pos": [4584.93896484375, 3064.856689453125], "size": [216.59999084472656, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "float_list", "type": "FLOAT", "link": 5523}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [5516]}], "properties": {"Node name for S&R": "FloatToSigmas"}, "widgets_values": []}, {"id": 1927, "type": "StringToFloatList", "pos": [4514.85400390625, 3187.75341796875], "size": [395.74224853515625, 88], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "FLOAT", "type": "FLOAT", "links": [5523]}], "properties": {"Node name for S&R": "StringToFloatList", "cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350"}, "widgets_values": ["0.85, 0.7250, 0.6, 0.4219, 0.0"], "color": "#223", "bgcolor": "#335"}, {"id": 1920, "type": "LTXVImgToVideoAdvanced", "pos": [1644.6727294921875, 2333.4248046875], "size": [277.20001220703125, 310], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 5493}, {"name": "negative", "type": "CONDITIONING", "link": 5494}, {"name": "vae", "type": "VAE", "link": 5495}, {"name": "image", "type": "IMAGE", "link": 5496}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [5497]}, {"name": "negative", "type": "CONDITIONING", "links": [5498]}, {"name": "latent", "type": "LATENT", "links": [5500]}], "properties": {"Node name for S&R": "LTXVImgToVideoAdvanced"}, "widgets_values": [768, 512, 97, 1, 30, 1, "bicubic", "center", 0.9]}, {"id": 1921, "type": "STGGuiderAdvanced", "pos": [2012.6072998046875, 2341.08251953125], "size": [278.79998779296875, 262], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5499}, {"name": "positive", "type": "CONDITIONING", "link": 5497}, {"name": "negative", "type": "CONDITIONING", "link": 5498}, {"name": "preset", "shape": 7, "type": "STG_ADVANCED_PRESET", "link": 5507}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [5503]}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.9970000000000002, true, "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "1,8,8,6,4,1", "0,0,0,0,0,0", "1, 1, 1, 1, 1, 1", "[25], [35], [35], [42], [42], [42]"]}, {"id": 73, "type": "KSamplerSelect", "pos": [2381.90234375, 2381.04150390625], "size": [251.94110107421875, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [5490]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["gradient_estimation"]}, {"id": 1949, "type": "PrintSigmas", "pos": [2413.2490234375, 2573.************], "size": [210, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "sigmas", "type": "SIGMAS", "link": 5568}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [5560]}], "properties": {"Node name for S&R": "PrintSigmas"}, "widgets_values": []}, {"id": 1809, "type": "STGAdvancedPresets", "pos": [2031.9530029296875, 2176.************], "size": [226.8000030517578, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "STG_ADVANCED_PRESET", "type": "STG_ADVANCED_PRESET", "links": [5507]}], "properties": {"Node name for S&R": "STGAdvancedPresets"}, "widgets_values": ["13b Dynamic"]}, {"id": 1906, "type": "SamplerCustomAdvanced", "pos": [2685.9462890625, 3434.73828125], "size": [236.8000030517578, 106], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 5574}, {"name": "guider", "type": "GUIDER", "link": 5474}, {"name": "sampler", "type": "SAMPLER", "link": 5455}, {"name": "sigmas", "type": "SIGMAS", "link": 5485}, {"name": "latent_image", "type": "LATENT", "link": 5505}], "outputs": [{"name": "output", "type": "LATENT", "links": null}, {"name": "denoised_output", "type": "LATENT", "links": [5466]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 1907, "type": "KSamplerSelect", "pos": [2341.2099609375, 3623.************], "size": [251.94110107421875, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [5455]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler_ancestral"]}, {"id": 1900, "type": "VHS_VideoCombine", "pos": [3400.************, 3057.************], "size": [471.0374755859375, 624.************], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "images", "shape": 7, "type": "IMAGE", "link": 5440}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv-base_00035.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24}}}}, {"id": 1335, "type": "VAEDecode", "pos": [3128.344970703125, 2317.790283203125], "size": [210, 46], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 5492}, {"name": "vae", "type": "VAE", "link": 5363}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [3578]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1870, "type": "Set VAE Decoder Noise", "pos": [2837.47265625, 2598.************], "size": [235.1999969482422, 130], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "vae", "type": "VAE", "link": 5362}], "outputs": [{"name": "VAE", "type": "VAE", "links": [5363, 5364, 5439]}], "properties": {"Node name for S&R": "Set VAE Decoder Noise"}, "widgets_values": [0.05000000000000001, 0.025000000000000005, 42, "fixed"]}, {"id": 1910, "type": "LTXVCropGuides", "pos": [2951.17919921875, 3285.184814453125], "size": [216.59999084472656, 66], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 5464}, {"name": "negative", "type": "CONDITIONING", "link": 5465}, {"name": "latent", "type": "LATENT", "link": 5466}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": null}, {"name": "negative", "type": "CONDITIONING", "links": null}, {"name": "latent", "type": "LATENT", "links": [5482, 5517, 5518]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "LTXVCropGuides"}, "widgets_values": []}, {"id": 1911, "type": "ImageResizeKJ", "pos": [1544.************, 3363.6826171875], "size": [210, 238], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 5469}, {"name": "width_input", "shape": 7, "type": "INT", "link": null}, {"name": "height_input", "shape": 7, "type": "INT", "link": null}, {"name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5470]}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 512, "bilinear", false, 2, "center"]}, {"id": 1896, "type": "CheckpointLoaderSimple", "pos": [1518.314453125, 3167.27880859375], "size": [315, 98], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [5471, 5513, 5520]}, {"name": "CLIP", "type": "CLIP", "links": null}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ltxv-13b-0.9.7-distilled.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 1950, "type": "VHS_VideoCombine", "pos": [6140, 2620], "size": [317.4000244140625, 522.8862915039062], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "images", "shape": 7, "type": "IMAGE", "link": 5561}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00963.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 24}}}}, {"id": 1903, "type": "FloatToSigmas", "pos": [2357.681640625, 3518.880615234375], "size": [216.59999084472656, 26], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "float_list", "type": "FLOAT", "link": 5442}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [5485]}], "properties": {"Node name for S&R": "FloatToSigmas"}, "widgets_values": []}, {"id": 1953, "type": "FloatToSigmas", "pos": [2220, 2730], "size": [216.59999084472656, 26], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "float_list", "type": "FLOAT", "link": 5567}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [5568]}], "properties": {"Node name for S&R": "FloatToSigmas"}, "widgets_values": []}, {"id": 1952, "type": "StringToFloatList", "pos": [1930, 2730], "size": [270, 90], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "FLOAT", "type": "FLOAT", "links": [5567]}], "properties": {"Node name for S&R": "StringToFloatList", "cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350"}, "widgets_values": ["1.0000, 0.9984, 0.9955, 0.9918, 0.9873, 0.9820, 0.9758, 0.9687, 0.9606, 0.9513"], "color": "#223", "bgcolor": "#335"}, {"id": 1206, "type": "LoadImage", "pos": [383.5616149902344, 2972.150390625], "size": [342.5999755859375, 314], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5377, 5469, 5496]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "LoadImage"}, "widgets_values": ["5402a9b614c9574530934d9a4e67a5c67e68258c1f86f5f268f0e6517d9006e3.jpg", "image"]}, {"id": 1507, "type": "RandomNoise", "pos": [2418.066162109375, 2169.15283203125], "size": [210, 82], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "slot_index": 0, "links": [5488]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "RandomNoise"}, "widgets_values": [118, "fixed"]}, {"id": 1919, "type": "SamplerCustomAdvanced", "pos": [2680, 2320], "size": [236.8000030517578, 106], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 5488}, {"name": "guider", "type": "GUIDER", "link": 5503}, {"name": "sampler", "type": "SAMPLER", "link": 5490}, {"name": "sigmas", "type": "SIGMAS", "link": 5560}, {"name": "latent_image", "type": "LATENT", "link": 5500}], "outputs": [{"name": "output", "type": "LATENT", "links": [5573]}, {"name": "denoised_output", "type": "LATENT", "links": [5492]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 1954, "type": "<PERSON><PERSON><PERSON><PERSON>", "pos": [2740, 3230], "size": [210, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [5574]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>"}}, {"id": 1901, "type": "StringToFloatList", "pos": [2060.************, 3514.4619140625], "size": [210, 88], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "FLOAT", "type": "FLOAT", "links": [5442]}], "properties": {"Node name for S&R": "StringToFloatList", "cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350"}, "widgets_values": ["0.950, 0.9094, 0.7250, 0.4219, 0.0"], "color": "#223", "bgcolor": "#335"}, {"id": 6, "type": "CLIPTextEncode", "pos": [759.8722534179688, 2616.82177734375], "size": [307.2346496582031, 204.2556610107422], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [3364]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#232", "bgcolor": "#353"}], "links": [[74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [3364, 6, 0, 1241, 0, "CONDITIONING"], [3365, 7, 0, 1241, 1, "CONDITIONING"], [3578, 1335, 0, 1336, 0, "IMAGE"], [4749, 1601, 0, 1661, 0, "IMAGE"], [4751, 1661, 0, 1699, 0, "IMAGE"], [5287, 44, 2, 1691, 2, "VAE"], [5297, 1691, 0, 1593, 0, "LATENT"], [5303, 1828, 0, 1691, 1, "UPSCALE_MODEL"], [5362, 44, 2, 1870, 0, "VAE"], [5363, 1870, 0, 1335, 1, "VAE"], [5364, 1870, 0, 1601, 1, "VAE"], [5366, 1241, 0, 1871, 1, "CONDITIONING"], [5367, 1241, 1, 1871, 2, "CONDITIONING"], [5370, 44, 2, 1872, 1, "VAE"], [5371, 1598, 0, 1872, 2, "NOISE"], [5374, 1871, 0, 1872, 5, "GUIDER"], [5377, 1206, 0, 1872, 7, "IMAGE"], [5416, 1872, 1, 1601, 0, "LATENT"], [5439, 1870, 0, 1899, 1, "VAE"], [5440, 1899, 0, 1900, 0, "IMAGE"], [5442, 1901, 0, 1903, 0, "FLOAT"], [5455, 1907, 0, 1906, 2, "SAMPLER"], [5459, 1241, 0, 1909, 0, "CONDITIONING"], [5460, 1241, 1, 1909, 1, "CONDITIONING"], [5461, 44, 2, 1909, 2, "VAE"], [5464, 1909, 0, 1910, 0, "CONDITIONING"], [5465, 1909, 1, 1910, 1, "CONDITIONING"], [5466, 1906, 1, 1910, 2, "LATENT"], [5469, 1206, 0, 1911, 0, "IMAGE"], [5470, 1911, 0, 1909, 4, "IMAGE"], [5471, 1896, 0, 1912, 0, "MODEL"], [5474, 1912, 0, 1906, 1, "GUIDER"], [5482, 1910, 2, 1899, 0, "LATENT"], [5485, 1903, 0, 1906, 3, "SIGMAS"], [5488, 1507, 0, 1919, 0, "NOISE"], [5490, 73, 0, 1919, 2, "SAMPLER"], [5492, 1919, 1, 1335, 0, "LATENT"], [5493, 1241, 0, 1920, 0, "CONDITIONING"], [5494, 1241, 1, 1920, 1, "CONDITIONING"], [5495, 44, 2, 1920, 2, "VAE"], [5496, 1206, 0, 1920, 3, "IMAGE"], [5497, 1920, 0, 1921, 1, "CONDITIONING"], [5498, 1920, 1, 1921, 2, "CONDITIONING"], [5499, 44, 0, 1921, 0, "MODEL"], [5500, 1920, 2, 1919, 4, "LATENT"], [5503, 1921, 0, 1919, 1, "GUIDER"], [5505, 1909, 2, 1906, 4, "LATENT"], [5507, 1809, 0, 1921, 3, "STG_ADVANCED_PRESET"], [5508, 1909, 0, 1912, 1, "CONDITIONING"], [5509, 1909, 1, 1912, 2, "CONDITIONING"], [5512, 1593, 0, 1872, 6, "LATENT"], [5513, 1896, 0, 1872, 0, "MODEL"], [5516, 1924, 0, 1872, 4, "SIGMAS"], [5517, 1910, 2, 1593, 1, "LATENT"], [5518, 1910, 2, 1691, 0, "LATENT"], [5519, 1926, 0, 1872, 3, "SAMPLER"], [5520, 1896, 0, 1871, 0, "MODEL"], [5523, 1927, 0, 1924, 0, "FLOAT"], [5560, 1949, 0, 1919, 3, "SIGMAS"], [5561, 1699, 0, 1950, 0, "IMAGE"], [5567, 1952, 0, 1953, 0, "FLOAT"], [5568, 1953, 0, 1949, 0, "SIGMAS"], [5573, 1919, 0, 1909, 3, "LATENT"], [5574, 1954, 0, 1906, 0, "NOISE"]], "groups": [{"id": 20, "title": "Base Low Res Generation ", "bounding": [1510, 2030, 2373.0302734375, 1658.0977783203125], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1336, 1899, 1948, 1912, 1909, 44, 1920, 1921, 73, 1949, 1809, 1906, 1907, 1900, 1335, 1870, 1910, 1911, 1896, 1903, 1953, 1952, 1507, 1919, 1954, 1901]}, {"id": 34, "title": "Latent Upscaler", "bounding": [4010, 2540, 364.39990234375, 604.2252197265625], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1828, 1863, 1593, 1691]}, {"id": 41, "title": "Setup", "bounding": [370, 2540, 1073.0299072265625, 752.9285888671875], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [38, 7, 1241, 1206, 6]}, {"id": 42, "title": "Add Details", "bounding": [4480, 2500, 2298.438232421875, 962.56298828125], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [1601, 1699, 1661, 1871, 1872, 1598, 1926, 1924, 1927, 1950]}], "config": {}, "extra": {"ds": {"scale": 1.0834705943388463, "offset": [-1580.073532993551, -2436.207321399802]}, "frontendVersion": "1.17.11", "VHS_latentpreview": true, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "prompt": {"6": {"inputs": {"text": "", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "38": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "44": {"inputs": {"ckpt_name": "ltxv-13b-0.9.7-dev.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "73": {"inputs": {"sampler_name": "gradient_estimation"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "1103": {"inputs": {"scheduler": "beta57", "steps": 30, "denoise": 1, "model": ["44", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "1206": {"inputs": {"image": "jncj17j2vo5e1.webp"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1241": {"inputs": {"frame_rate": 24.000000000000004, "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "1335": {"inputs": {"samples": ["1919", 1], "vae": ["1870", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1336": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "images": ["1335", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1507": {"inputs": {"noise_seed": 114}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1593": {"inputs": {"factor": 0.25, "latents": ["1691", 0], "reference": ["1910", 2]}, "class_type": "LTXVAdainLatent", "_meta": {"title": "🅛🅣🅧 LTXV Adain Latent"}}, "1598": {"inputs": {"noise_seed": 421}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1599": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-hd", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 18, "save_metadata": false, "pingpong": false, "save_output": false, "images": ["1699", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1601": {"inputs": {"tile_size": 1280, "overlap": 128, "temporal_size": 256, "temporal_overlap": 32, "samples": ["1872", 1], "vae": ["1870", 0]}, "class_type": "VAEDecodeTiled", "_meta": {"title": "VAE Decode (Tiled)"}}, "1661": {"inputs": {"width": 1280, "height": 1280, "upscale_method": "bicubic", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["1601", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "1691": {"inputs": {"samples": ["1910", 2], "upscale_model": ["1828", 0], "vae": ["44", 2]}, "class_type": "LTXVLatentUpsampler", "_meta": {"title": "🅛🅣🅧 LTXV Latent Upsampler"}}, "1699": {"inputs": {"grain_intensity": 0.010000000000000002, "saturation": 0.5, "images": ["1661", 0]}, "class_type": "LTXVFilmGrain", "_meta": {"title": "🅛🅣🅧 LTXV Film Grain"}}, "1809": {"inputs": {"preset": "13b Dynamic"}, "class_type": "STGAdvancedPresets", "_meta": {"title": "🅛🅣🅧 STG Advanced Presets"}}, "1828": {"inputs": {"upscale_model": "ltxv-spatial-upscaler-0.9.7.safetensors", "spatial_upsample": true, "temporal_upsample": false}, "class_type": "LTXVLatentUpsamplerModelLoader", "_meta": {"title": "🅛🅣🅧 LTXV Latent Upsampler Model Loader"}}, "1870": {"inputs": {"timestep": 0.05000000000000001, "scale": 0.025000000000000005, "seed": 42, "vae": ["44", 2]}, "class_type": "Set VAE Decoder Noise", "_meta": {"title": "🅛🅣🅧 Set VAE Decoder Noise"}}, "1871": {"inputs": {"skip_steps_sigma_threshold": 0.9970000000000002, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "1,1,1,1,1,1", "stg_scale_values": "0,0,0,0,0,0", "stg_rescale_values": "1,1,1,1,1,1", "stg_layers_indices": "[41],[42],[42],[42],[30],[30]", "model": ["1896", 0], "positive": ["1241", 0], "negative": ["1241", 1]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "1872": {"inputs": {"horizontal_tiles": 1, "vertical_tiles": 1, "overlap": 3, "latents_cond_strength": 0.15, "boost_latent_similarity": false, "crop": "center", "optional_cond_indices": "0", "images_cond_strengths": "0.9", "model": ["1896", 0], "vae": ["44", 2], "noise": ["1598", 0], "sampler": ["1926", 0], "sigmas": ["1924", 0], "guider": ["1871", 0], "latents": ["1593", 0], "optional_cond_images": ["1206", 0]}, "class_type": "LTXVTiledSampler", "_meta": {"title": "🅛🅣🅧 LTXV Tiled Sampler"}}, "1895": {"inputs": {"step": 10, "sigmas": ["1103", 0]}, "class_type": "SplitSigmas", "_meta": {"title": "SplitSigmas"}}, "1896": {"inputs": {"ckpt_name": "ltxv-13b-0.9.7-distilled.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "1899": {"inputs": {"samples": ["1910", 2], "vae": ["1870", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1900": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "images": ["1899", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1901": {"inputs": {"string": " 0.950, 0.9094, 0.7250, 0.4219, 0.0"}, "class_type": "StringToFloatList", "_meta": {"title": "String to Float List"}}, "1903": {"inputs": {"float_list": ["1901", 0]}, "class_type": "FloatToSigmas", "_meta": {"title": "Float To Sigmas"}}, "1906": {"inputs": {"noise": ["1922", 0], "guider": ["1912", 0], "sampler": ["1907", 0], "sigmas": ["1903", 0], "latent_image": ["1909", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "1907": {"inputs": {"sampler_name": "euler_ancestral"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "1909": {"inputs": {"frame_idx": 0, "strength": 0.9000000000000001, "positive": ["1241", 0], "negative": ["1241", 1], "vae": ["44", 2], "latent": ["1919", 0], "image": ["1911", 0]}, "class_type": "LTXVAddGuide", "_meta": {"title": "LTXVAddGuide"}}, "1910": {"inputs": {"positive": ["1909", 0], "negative": ["1909", 1], "latent": ["1906", 1]}, "class_type": "LTXVCropGuides", "_meta": {"title": "LTXVCropGuides"}}, "1911": {"inputs": {"width": 768, "height": 512, "upscale_method": "bilinear", "keep_proportion": false, "divisible_by": 2, "crop": "center", "image": ["1206", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "1912": {"inputs": {"cfg": 1, "model": ["1896", 0], "positive": ["1909", 0], "negative": ["1909", 1]}, "class_type": "CFGGuider", "_meta": {"title": "CFGGuider"}}, "1919": {"inputs": {"noise": ["1507", 0], "guider": ["1921", 0], "sampler": ["73", 0], "sigmas": ["1949", 0], "latent_image": ["1920", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "1920": {"inputs": {"width": 768, "height": 512, "length": 97, "batch_size": 1, "crf": 30, "blur_radius": 1, "interpolation": "bicubic", "crop": "center", "strength": 0.9, "positive": ["1241", 0], "negative": ["1241", 1], "vae": ["44", 2], "image": ["1206", 0]}, "class_type": "LTXVImgToVideoAdvanced", "_meta": {"title": "🅛🅣🅧 LTXV Img To Video Advanced"}}, "1921": {"inputs": {"skip_steps_sigma_threshold": 0.9970000000000002, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "1,8,8,6,4,1", "stg_scale_values": "0,0,0,0,0,0", "stg_rescale_values": "1, 1, 1, 1, 1, 1", "stg_layers_indices": "[25], [35], [35], [42], [42], [42]", "model": ["44", 0], "positive": ["1920", 0], "negative": ["1920", 1], "preset": ["1809", 0]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "1922": {"inputs": {}, "class_type": "<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON>"}}, "1924": {"inputs": {"float_list": ["1927", 0]}, "class_type": "FloatToSigmas", "_meta": {"title": "Float To Sigmas"}}, "1926": {"inputs": {"sampler_name": "euler_ancestral"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "1927": {"inputs": {"string": "0.85, 0.7250, 0.6, 0.4219, 0.0"}, "class_type": "StringToFloatList", "_meta": {"title": "String to Float List"}}, "1949": {"inputs": {"sigmas": ["1895", 0]}, "class_type": "PrintSigmas", "_meta": {"title": "PrintSigmas"}}}, "comfy_fork_version": "develop@580b3007", "workspace_info": {"id": "elBQFQknIoLYTEwIloQuw"}, "node_versions": {"comfy-core": "0.3.20"}}, "version": 0.4}