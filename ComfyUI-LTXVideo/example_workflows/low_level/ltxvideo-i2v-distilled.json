{"id": "d8be2e45-0fd2-4f4a-8065-469fdf4bc018", "revision": 0, "last_node_id": 109, "last_link_id": 268, "nodes": [{"id": 38, "type": "CLIPLoader", "pos": [60, 190], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 8, "type": "VAEDecode", "pos": [1800, 430], "size": [210, 46], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 255}, {"name": "vae", "type": "VAE", "link": 87}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [261]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 103, "type": "VHS_VideoCombine", "pos": [1789.6087646484375, 548.7745361328125], "size": [315, 545], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "images", "shape": 7, "type": "IMAGE", "link": 261}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "972c87da577b47211c4e9aeed30dc38c7bae607f", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 20, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv_00058.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 24, "workflow": "ltxv_00058.png", "fullpath": "C:\\Users\\<USER>\\Projects\\ComfyUI\\output\\ltxv_00058.mp4"}}}}, {"id": 82, "type": "LTXVPreprocess", "pos": [488.92791748046875, 629.9364624023438], "size": [275.9266662597656, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 226}], "outputs": [{"name": "output_image", "type": "IMAGE", "slot_index": 0, "links": [248]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LTXVPreprocess"}, "widgets_values": [38]}, {"id": 7, "type": "CLIPTextEncode", "pos": [420, 390], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [240]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 78, "type": "LoadImage", "pos": [40, 630], "size": [385.15606689453125, 333.3305358886719], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [226]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["fox.jpg", "image", ""]}, {"id": 6, "type": "CLIPTextEncode", "pos": [420, 180], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [239]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A red fox moving gracefully, its russet coat vibrant against the white landscape, leaving perfect star-shaped prints behind as steam rises from its breath in the crisp winter air. The scene is wrapped in snow-muffled silence, broken only by the gentle murmur of water still flowing beneath the ice."], "color": "#232", "bgcolor": "#353"}, {"id": 76, "type": "Note", "pos": [36.85359573364258, 360.48809814453125], "size": [360, 200], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["While LTXV-2b model prefers long descriptive prompts, this version supports experimentation with broader prompting styles."], "color": "#432", "bgcolor": "#653"}, {"id": 69, "type": "LTXVConditioning", "pos": [1183.358642578125, 292.5882873535156], "size": [223.8660125732422, 78], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 245}, {"name": "negative", "type": "CONDITIONING", "link": 246}], "outputs": [{"name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [256]}, {"name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [257]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LTXVConditioning"}, "widgets_values": [24.000000000000004]}, {"id": 101, "type": "SamplerCustomAdvanced", "pos": [1780, 270], "size": [236.8000030517578, 106], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 260}, {"name": "guider", "type": "GUIDER", "link": 252}, {"name": "sampler", "type": "SAMPLER", "link": 253}, {"name": "sigmas", "type": "SIGMAS", "link": 268}, {"name": "latent_image", "type": "LATENT", "link": 259}], "outputs": [{"name": "output", "type": "LATENT", "slot_index": 0, "links": []}, {"name": "denoised_output", "type": "LATENT", "slot_index": 1, "links": [255]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 95, "type": "LTXVImgToVideo", "pos": [900, 290], "size": [210, 190], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 239}, {"name": "negative", "type": "CONDITIONING", "link": 240}, {"name": "vae", "type": "VAE", "link": 250}, {"name": "image", "type": "IMAGE", "link": 248}], "outputs": [{"name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [245]}, {"name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [246]}, {"name": "latent", "type": "LATENT", "slot_index": 2, "links": [259]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LTXVImgToVideo"}, "widgets_values": [768, 512, 97, 1]}, {"id": 100, "type": "StringToFloatList", "pos": [1116.0089111328125, 604.5989379882812], "size": [395.74224853515625, 88], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "FLOAT", "type": "FLOAT", "links": [251]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350", "Node name for S&R": "StringToFloatList"}, "widgets_values": ["1.0000, 0.9937, 0.9875, 0.9812, 0.9750, 0.9094, 0.7250, 0.4219, 0.0"], "color": "#223", "bgcolor": "#335"}, {"id": 105, "type": "Note", "pos": [1126.7423095703125, 424.2294616699219], "size": [335.8657531738281, 106.6832046508789], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Distilled model expects the following sigma schedule:\n1.0000, 0.9937, 0.9875, 0.9812, 0.9750, 0.9094, 0.7250, 0.4219, 0.0\n\n\nEuler_ancestral is the recommended default sampler."], "color": "#432", "bgcolor": "#653"}, {"id": 97, "type": "KSamplerSelect", "pos": [1477.55615234375, 427.04132080078125], "size": [275.************, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "slot_index": 0, "links": [253]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler_ancestral"]}, {"id": 99, "type": "CFGGuider", "pos": [1536.1513671875, 280], "size": [210, 98], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 258}, {"name": "positive", "type": "CONDITIONING", "link": 256}, {"name": "negative", "type": "CONDITIONING", "link": 257}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [252]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CFGGuider"}, "widgets_values": [1]}, {"id": 102, "type": "RandomNoise", "pos": [1524.6190185546875, 138.8462371826172], "size": [210, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [260]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "RandomNoise"}, "widgets_values": [45, "fixed"]}, {"id": 98, "type": "FloatToSigmas", "pos": [1574.70166015625, 620.4625244140625], "size": [210, 58], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [{"name": "float_list", "type": "FLOAT", "widget": {"name": "float_list"}, "link": null}, {"name": "float_list", "type": "FLOAT", "widget": {"name": "float_list"}, "link": 251}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [268]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350", "Node name for S&R": "FloatToSigmas"}, "widgets_values": [0]}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [899.0839233398438, 113.54173278808594], "size": [315, 98], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [258]}, {"name": "CLIP", "type": "CLIP", "links": null}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [87, 250]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ltxv-2b-0.9.6-distilled-04-25.safetensors"]}], "links": [[74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [87, 44, 2, 8, 1, "VAE"], [226, 78, 0, 82, 0, "IMAGE"], [239, 6, 0, 95, 0, "CONDITIONING"], [240, 7, 0, 95, 1, "CONDITIONING"], [245, 95, 0, 69, 0, "CONDITIONING"], [246, 95, 1, 69, 1, "CONDITIONING"], [248, 82, 0, 95, 3, "IMAGE"], [250, 44, 2, 95, 2, "VAE"], [251, 100, 0, 98, 1, "FLOAT"], [252, 99, 0, 101, 1, "GUIDER"], [253, 97, 0, 101, 2, "SAMPLER"], [255, 101, 1, 8, 0, "LATENT"], [256, 69, 0, 99, 1, "CONDITIONING"], [257, 69, 1, 99, 2, "CONDITIONING"], [258, 44, 0, 99, 0, "MODEL"], [259, 95, 2, 101, 4, "LATENT"], [260, 102, 0, 101, 0, "NOISE"], [261, 8, 0, 103, 0, "IMAGE"], [268, 98, 0, 101, 3, "SIGMAS"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6303940863129158, "offset": [627.9092558924768, 478.72279841993054]}, "VHS_latentpreview": true, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}