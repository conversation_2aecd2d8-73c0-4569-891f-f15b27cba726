{"last_node_id": 90, "last_link_id": 220, "nodes": [{"id": 6, "type": "CLIPTextEncode", "pos": [475.5454406738281, 199.1752471923828], "size": [295.2641296386719, 78], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 74}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 207}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [203], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": [""], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [477.51873779296875, 259.2063293457031], "size": [425.27801513671875, 180.6060791015625], "flags": {"collapsed": true}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [204], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 85, "type": "LTXVPromptEnhancerLoader", "pos": [-38.702144622802734, 346.9218444824219], "size": [428.4000244140625, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "prompt_enhancer", "localized_name": "prompt_enhancer", "type": "LTXV_PROMPT_ENHANCER", "links": [206], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVPromptEnhancerLoader"}, "widgets_values": ["unsloth/Llama-3.2-3B-Instruct", "MiaoshouAI/Florence-2-large-PromptGen-v2.0"]}, {"id": 84, "type": "EmptyLTXVLatentVideo", "pos": [865.0157470703125, 427.244384765625], "size": [315, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "localized_name": "LATENT", "type": "LATENT", "links": [205, 210], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLTXVLatentVideo", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": [768, 512, 97, 1]}, {"id": 38, "type": "CLIPLoader", "pos": [73.48588562011719, 131.20065307617188], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "localized_name": "CLIP", "type": "CLIP", "links": [74, 75], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 83, "type": "RandomNoise", "pos": [451.1808166503906, -72.91265106201172], "size": [315, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "localized_name": "NOISE", "type": "NOISE", "links": [194], "slot_index": 0}], "properties": {"Node name for S&R": "RandomNoise", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": [42, "fixed"]}, {"id": 73, "type": "KSamplerSelect", "pos": [830.7018432617188, -151.5533905029297], "size": [315, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "localized_name": "SAMPLER", "type": "SAMPLER", "links": [196], "slot_index": 0}], "properties": {"Node name for S&R": "KSamplerSelect", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["gradient_estimation"]}, {"id": 81, "type": "SamplerCustomAdvanced", "pos": [1206.2567138671875, 111.3145523071289], "size": [355.20001220703125, 106], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "noise", "localized_name": "noise", "type": "NOISE", "link": 194}, {"name": "guider", "localized_name": "guider", "type": "GUIDER", "link": 218}, {"name": "sampler", "localized_name": "sampler", "type": "SAMPLER", "link": 196}, {"name": "sigmas", "localized_name": "sigmas", "type": "SIGMAS", "link": 197}, {"name": "latent_image", "localized_name": "latent_image", "type": "LATENT", "link": 205}], "outputs": [{"name": "output", "localized_name": "output", "type": "LATENT", "links": [199], "slot_index": 0}, {"name": "denoised_output", "localized_name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": []}, {"id": 71, "type": "LTXVScheduler", "pos": [1210.************, -119.96339416503906], "size": [315, 154], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "latent", "localized_name": "latent", "type": "LATENT", "shape": 7, "link": 210}], "outputs": [{"name": "SIGMAS", "localized_name": "SIGMAS", "type": "SIGMAS", "links": [197], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVScheduler", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": [20, 2.05, 0.95, true, 0.1]}, {"id": 8, "type": "VAEDecode", "pos": [1268.183349609375, 275.46270751953125], "size": [210, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "localized_name": "samples", "type": "LATENT", "link": 199}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 211}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [212], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": []}, {"id": 88, "type": "VHS_VideoCombine", "pos": [1592.291748046875, -89.84502410888672], "size": [315, 520.6666870117188], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "images", "localized_name": "images", "type": "IMAGE", "shape": 7, "link": 212}, {"name": "audio", "localized_name": "audio", "type": "AUDIO", "shape": 7, "link": null}, {"name": "meta_batch", "localized_name": "meta_batch", "type": "VHS_BatchManager", "shape": 7, "link": null}, {"name": "vae", "localized_name": "vae", "type": "VAE", "shape": 7, "link": null}], "outputs": [{"name": "Filenames", "localized_name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 25, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv_00128.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 25}}}}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [93.61398315429688, -66.24134063720703], "size": [315, 98], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [217], "slot_index": 0}, {"name": "CLIP", "localized_name": "CLIP", "type": "CLIP", "links": null}, {"name": "VAE", "localized_name": "VAE", "type": "VAE", "links": [211], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["ltxv-2b-0.9.6-dev-04-25.safetensors"]}, {"id": 90, "type": "STGGuiderAdvanced", "pos": [830.8892822265625, -37.005123138427734], "size": [327.5999755859375, 242], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 217}, {"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 219}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 220}], "outputs": [{"name": "GUIDER", "localized_name": "GUIDER", "type": "GUIDER", "links": [218], "slot_index": 0}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.996, true, "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "4, 4, 4, 4, 1, 1", "2, 2, 2, 2, 1, 0", "1, 1, 1, 1, 1, 1", "[14], [14], [14], [14], [14], [14]"]}, {"id": 69, "type": "LTXVConditioning", "pos": [900.875, 273.27288818359375], "size": [223.8660125732422, 78], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 203}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 204}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [219], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [220], "slot_index": 1}], "properties": {"Node name for S&R": "LTXVConditioning", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": [25]}, {"id": 86, "type": "LTXVPromptEnhancer", "pos": [447.4213562011719, 346.47674560546875], "size": [335.70489501953125, 124.35137939453125], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "prompt_enhancer", "localized_name": "prompt_enhancer", "type": "LTXV_PROMPT_ENHANCER", "link": 206}, {"name": "image_prompt", "localized_name": "image_prompt", "type": "IMAGE", "shape": 7, "link": null}], "outputs": [{"name": "str", "localized_name": "str", "type": "STRING", "links": [207], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVPromptEnhancer"}, "widgets_values": ["sleeping cat", 256]}], "links": [[74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [194, 83, 0, 81, 0, "NOISE"], [196, 73, 0, 81, 2, "SAMPLER"], [197, 71, 0, 81, 3, "SIGMAS"], [199, 81, 0, 8, 0, "LATENT"], [203, 6, 0, 69, 0, "CONDITIONING"], [204, 7, 0, 69, 1, "CONDITIONING"], [205, 84, 0, 81, 4, "LATENT"], [206, 85, 0, 86, 0, "LTXV_PROMPT_ENHANCER"], [207, 86, 0, 6, 1, "STRING"], [210, 84, 0, 71, 0, "LATENT"], [211, 44, 2, 8, 1, "VAE"], [212, 8, 0, 88, 0, "IMAGE"], [217, 44, 0, 90, 0, "MODEL"], [218, 90, 0, 81, 1, "GUIDER"], [219, 69, 0, 90, 1, "CONDITIONING"], [220, 69, 1, 90, 2, "CONDITIONING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6934334949441353, "offset": [217.20361259812148, 342.27826789342566]}, "node_versions": {"comfy-core": "0.3.27"}, "prompt": {"6": {"inputs": {"text": ["86", 0], "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["81", 0], "vae": ["44", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "38": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "44": {"inputs": {"ckpt_name": "ltxv-2b-0.9.6-dev-04-25.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "69": {"inputs": {"frame_rate": 25, "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "71": {"inputs": {"steps": 20, "max_shift": 2.05, "base_shift": 0.95, "stretch": true, "terminal": 0.1, "latent": ["84", 0]}, "class_type": "LTXVScheduler", "_meta": {"title": "LTXVScheduler"}}, "73": {"inputs": {"sampler_name": "gradient_estimation"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "81": {"inputs": {"noise": ["83", 0], "guider": ["90", 0], "sampler": ["73", 0], "sigmas": ["71", 0], "latent_image": ["84", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "83": {"inputs": {"noise_seed": 42}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "84": {"inputs": {"width": 768, "height": 512, "length": 97, "batch_size": 1}, "class_type": "EmptyLTXVLatentVideo", "_meta": {"title": "EmptyLTXVLatentVideo"}}, "85": {"inputs": {"llm_name": "unsloth/Llama-3.2-3B-Instruct", "image_captioner_name": "MiaoshouAI/Florence-2-large-PromptGen-v2.0"}, "class_type": "LTXVPromptEnhancerLoader", "_meta": {"title": "🅛🅣🅧 LTXV Prompt Enhancer Loader"}}, "86": {"inputs": {"prompt": "sleeping cat", "max_resulting_tokens": 256, "prompt_enhancer": ["85", 0]}, "class_type": "LTXVPromptEnhancer", "_meta": {"title": "🅛🅣🅧 LTXV Prompt Enhancer"}}, "88": {"inputs": {"frame_rate": 25, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "images": ["8", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "90": {"inputs": {"skip_steps_sigma_threshold": 0.996, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "4, 4, 4, 4, 1, 1", "stg_scale_values": "2, 2, 2, 2, 1, 0", "stg_rescale_values": "1, 1, 1, 1, 1, 1", "stg_layers_indices": "[14], [14], [14], [14], [14], [14]", "model": ["44", 0], "positive": ["69", 0], "negative": ["69", 1]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}}, "comfy_fork_version": "feature/memory_prof@f7e64946"}, "version": 0.4}