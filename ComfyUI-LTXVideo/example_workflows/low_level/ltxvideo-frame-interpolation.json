{"last_node_id": 38, "last_link_id": 93, "nodes": [{"id": 26, "type": "LTXVAddGuide", "pos": [1081.6207275390625, 1194.************], "size": [315, 162], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 59}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 60}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 74}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 62}, {"name": "image", "localized_name": "image", "type": "IMAGE", "link": 63}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [66], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [67], "slot_index": 1}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [68], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVAddGuide"}, "widgets_values": [0, 1]}, {"id": 19, "type": "LTXVPreprocess", "pos": [1083.0303955078125, 1428.751953125], "size": [315, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 81}], "outputs": [{"name": "output_image", "localized_name": "output_image", "type": "IMAGE", "links": [63], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVPreprocess"}, "widgets_values": [40]}, {"id": 20, "type": "LTXVPreprocess", "pos": [1501.54248046875, 1427.39453125], "size": [315, 58], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 83}], "outputs": [{"name": "output_image", "localized_name": "output_image", "type": "IMAGE", "links": [69], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVPreprocess"}, "widgets_values": [40]}, {"id": 31, "type": "LTXVScheduler", "pos": [1889.************, 1356.0611572265625], "size": [315, 154], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "latent", "localized_name": "latent", "type": "LATENT", "shape": 7, "link": 78}], "outputs": [{"name": "SIGMAS", "localized_name": "SIGMAS", "type": "SIGMAS", "links": [79], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVScheduler"}, "widgets_values": [20, 2.05, 0.95, true, 0.1]}, {"id": 2, "type": "LoadImage", "pos": [1091.1929931640625, 1840.934814453125], "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [80], "slot_index": 0}, {"name": "MASK", "localized_name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["start.jpg", "image"]}, {"id": 1, "type": "LoadImage", "pos": [1502.8370361328125, 1844.201171875], "size": [315, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [82], "slot_index": 0}, {"name": "MASK", "localized_name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["end.jpg", "image"]}, {"id": 35, "type": "ImageResizeKJ", "pos": [1499.1163330078125, 1531.5040283203125], "size": [315, 286], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 82}, {"name": "get_image_size", "localized_name": "get_image_size", "type": "IMAGE", "shape": 7, "link": null}, {"name": "width_input", "type": "INT", "shape": 7, "pos": [10, 196], "widget": {"name": "width_input"}, "link": null}, {"name": "height_input", "type": "INT", "shape": 7, "pos": [10, 220], "widget": {"name": "height_input"}, "link": null}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [83], "slot_index": 0}, {"name": "width", "localized_name": "width", "type": "INT", "links": null}, {"name": "height", "localized_name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 512, "nearest-exact", false, 2, 0, 0, "center"]}, {"id": 28, "type": "Note", "pos": [1095.3414306640625, 2204.1455078125], "size": [314.0157470703125, 91.49161529541016], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["First frame conditioning, the frame size shoudl match the generated video aspect ration."], "color": "#432", "bgcolor": "#653"}, {"id": 29, "type": "Note", "pos": [1510.7900390625, 2206.3818359375], "size": [304.03729248046875, 88], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Last frame conditioning, the frame size shoudl match the generated video aspect ration."], "color": "#432", "bgcolor": "#653"}, {"id": 27, "type": "LTXVAddGuide", "pos": [1491.158935546875, 1197.************], "size": [315, 162], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 66}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 67}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 75}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 68}, {"name": "image", "localized_name": "image", "type": "IMAGE", "link": 69}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [70], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [71], "slot_index": 1}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [72, 78], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVAddGuide"}, "widgets_values": [120, 1]}, {"id": 10, "type": "LTXVConditioning", "pos": [1881.************, 1204.************], "size": [315, 78], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 70}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 71}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [87, 90], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [88, 91], "slot_index": 1}], "properties": {"Node name for S&R": "LTXVConditioning"}, "widgets_values": [25]}, {"id": 38, "type": "STGGuiderAdvanced", "pos": [1886.081787109375, 1587.************], "size": [327.5999755859375, 242], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 92}, {"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 90}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 91}], "outputs": [{"name": "GUIDER", "localized_name": "GUIDER", "type": "GUIDER", "links": [93], "slot_index": 0}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.996, true, "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "4, 4, 4, 4, 1, 1", "2, 2, 2, 2, 1, 0", "1, 1, 1, 1, 1, 1", "[14], [14], [14], [14], [14], [14]"]}, {"id": 7, "type": "SamplerCustomAdvanced", "pos": [2242.************, 1192.1341552734375], "size": [355.20001220703125, 106], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "noise", "localized_name": "noise", "type": "NOISE", "link": 3}, {"name": "guider", "localized_name": "guider", "type": "GUIDER", "link": 93}, {"name": "sampler", "localized_name": "sampler", "type": "SAMPLER", "link": 4}, {"name": "sigmas", "localized_name": "sigmas", "type": "SIGMAS", "link": 79}, {"name": "latent_image", "localized_name": "latent_image", "type": "LATENT", "link": 72}], "outputs": [{"name": "output", "localized_name": "output", "type": "LATENT", "links": [85], "slot_index": 0}, {"name": "denoised_output", "localized_name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 37, "type": "LTXVCropGuides", "pos": [2292.63720703125, 1359.************], "size": [216.59999084472656, 66], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 87}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 88}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 85}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": null}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": null}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [86], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVCropGuides"}, "widgets_values": []}, {"id": 17, "type": "VAEDecode", "pos": [2298.************, 1490.08056640625], "size": [210, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "samples", "localized_name": "samples", "type": "LATENT", "link": 86}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 73}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [84], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 36, "type": "VHS_VideoCombine", "pos": [2296.************, 1605.************], "size": [315, 520.6666870117188], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "images", "localized_name": "images", "type": "IMAGE", "shape": 7, "link": 84}, {"name": "audio", "localized_name": "audio", "type": "AUDIO", "shape": 7, "link": null}, {"name": "meta_batch", "localized_name": "meta_batch", "type": "VHS_BatchManager", "shape": 7, "link": null}, {"name": "vae", "localized_name": "vae", "type": "VAE", "shape": 7, "link": null}], "outputs": [{"name": "Filenames", "localized_name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 25, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv_00121.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 25}}}}, {"id": 34, "type": "ImageResizeKJ", "pos": [1090.9002685546875, 1524.03857421875], "size": [315, 286], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 80}, {"name": "get_image_size", "localized_name": "get_image_size", "type": "IMAGE", "shape": 7, "link": null}, {"name": "width_input", "type": "INT", "shape": 7, "pos": [10, 196], "widget": {"name": "width_input"}, "link": null}, {"name": "height_input", "type": "INT", "shape": 7, "pos": [10, 220], "widget": {"name": "height_input"}, "link": null}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [81], "slot_index": 0}, {"name": "width", "localized_name": "width", "type": "INT", "links": null}, {"name": "height", "localized_name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 512, "nearest-exact", false, 2, 0, 0, "center"]}, {"id": 13, "type": "EmptyLTXVLatentVideo", "pos": [658.8583374023438, 1901.9586181640625], "size": [315, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "localized_name": "LATENT", "type": "LATENT", "links": [62], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLTXVLatentVideo"}, "widgets_values": [768, 512, 121, 1]}, {"id": 15, "type": "CLIPTextEncode", "pos": [629.337158203125, 1725.8612060546875], "size": [392.1722717285156, 119.6710433959961], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 19}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [60], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["shaky, glitchy, low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly, transition"]}, {"id": 14, "type": "CLIPTextEncode", "pos": [624.9329833984375, 1471.5355224609375], "size": [400, 200], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 18}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [59], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A donkey gracefully rides a surfboard, going from a fiery volcanic wave to a frozen, icy landscape. Initially, the dark brown donkey stands on a surfboard amidst a wave of lava, the intense orange flames illuminating its silhouette. The camera maintains a medium shot, slightly angled, capturing the donkey's determined expression. The scene shifts smoothly to a wide shot of the donkey on the same surfboard, now gliding across a frozen lake with scattered ice floes and snow-covered rocks. The lighting changes to a bright, clear winter day. The donkey's expression remains calm; its movement is smooth and controlled. The video is computer-generated imagery.\n\n\n\n\n\n\n"]}, {"id": 16, "type": "CLIPLoader", "pos": [657.5050048828125, 1323.1318359375], "size": [315, 98], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "localized_name": "CLIP", "type": "CLIP", "links": [18, 19], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 8, "type": "RandomNoise", "pos": [1891.2080078125, 1895.3349609375], "size": [315, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "localized_name": "NOISE", "type": "NOISE", "links": [3], "slot_index": 0}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [5, "fixed"]}, {"id": 9, "type": "KSamplerSelect", "pos": [1896.1917724609375, 2035.409423828125], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "localized_name": "SAMPLER", "type": "SAMPLER", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["gradient_estimation"]}, {"id": 12, "type": "CheckpointLoaderSimple", "pos": [655.3289794921875, 1178.0711669921875], "size": [315, 98], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [92], "slot_index": 0}, {"name": "CLIP", "localized_name": "CLIP", "type": "CLIP", "links": null}, {"name": "VAE", "localized_name": "VAE", "type": "VAE", "links": [73, 74, 75], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ltxv-2b-0.9.6-dev-04-25.safetensors"]}], "links": [[3, 8, 0, 7, 0, "NOISE"], [4, 9, 0, 7, 2, "SAMPLER"], [18, 16, 0, 14, 0, "CLIP"], [19, 16, 0, 15, 0, "CLIP"], [59, 14, 0, 26, 0, "CONDITIONING"], [60, 15, 0, 26, 1, "CONDITIONING"], [62, 13, 0, 26, 3, "LATENT"], [63, 19, 0, 26, 4, "IMAGE"], [66, 26, 0, 27, 0, "CONDITIONING"], [67, 26, 1, 27, 1, "CONDITIONING"], [68, 26, 2, 27, 3, "LATENT"], [69, 20, 0, 27, 4, "IMAGE"], [70, 27, 0, 10, 0, "CONDITIONING"], [71, 27, 1, 10, 1, "CONDITIONING"], [72, 27, 2, 7, 4, "LATENT"], [73, 12, 2, 17, 1, "VAE"], [74, 12, 2, 26, 2, "VAE"], [75, 12, 2, 27, 2, "VAE"], [78, 27, 2, 31, 0, "LATENT"], [79, 31, 0, 7, 3, "SIGMAS"], [80, 2, 0, 34, 0, "IMAGE"], [81, 34, 0, 19, 0, "IMAGE"], [82, 1, 0, 35, 0, "IMAGE"], [83, 35, 0, 20, 0, "IMAGE"], [84, 17, 0, 36, 0, "IMAGE"], [85, 7, 0, 37, 2, "LATENT"], [86, 37, 2, 17, 0, "LATENT"], [87, 10, 0, 37, 0, "CONDITIONING"], [88, 10, 1, 37, 1, "CONDITIONING"], [90, 10, 0, 38, 1, "CONDITIONING"], [91, 10, 1, 38, 2, "CONDITIONING"], [92, 12, 0, 38, 0, "MODEL"], [93, 38, 0, 7, 1, "GUIDER"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6303940863128502, "offset": [-355.************, -1116.3282097448212]}, "node_versions": {"comfy-core": "0.3.27"}, "prompt": {"1": {"inputs": {"image": "end.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "2": {"inputs": {"image": "start.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "7": {"inputs": {"noise": ["8", 0], "guider": ["38", 0], "sampler": ["9", 0], "sigmas": ["31", 0], "latent_image": ["27", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "8": {"inputs": {"noise_seed": 5}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "9": {"inputs": {"sampler_name": "gradient_estimation"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "10": {"inputs": {"frame_rate": 25, "positive": ["27", 0], "negative": ["27", 1]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "12": {"inputs": {"ckpt_name": "ltxv-2b-0.9.6-dev-04-25.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "13": {"inputs": {"width": 768, "height": 512, "length": 121, "batch_size": 1}, "class_type": "EmptyLTXVLatentVideo", "_meta": {"title": "EmptyLTXVLatentVideo"}}, "14": {"inputs": {"text": "A donkey gracefully rides a surfboard, going from a fiery volcanic wave to a frozen, icy landscape. Initially, the dark brown donkey stands on a surfboard amidst a wave of lava, the intense orange flames illuminating its silhouette. The camera maintains a medium shot, slightly angled, capturing the donkey's determined expression. The scene shifts smoothly to a wide shot of the donkey on the same surfboard, now gliding across a frozen lake with scattered ice floes and snow-covered rocks. The lighting changes to a bright, clear winter day. The donkey's expression remains calm; its movement is smooth and controlled. The video is computer-generated imagery.\n\n\n\n\n\n\n", "clip": ["16", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "15": {"inputs": {"text": "shaky, glitchy, low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly, transition", "clip": ["16", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "16": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "17": {"inputs": {"samples": ["37", 2], "vae": ["12", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "19": {"inputs": {"img_compression": 40, "image": ["34", 0]}, "class_type": "LTXVPreprocess", "_meta": {"title": "LTXVPreprocess"}}, "20": {"inputs": {"img_compression": 40, "image": ["35", 0]}, "class_type": "LTXVPreprocess", "_meta": {"title": "LTXVPreprocess"}}, "26": {"inputs": {"frame_idx": 0, "strength": 1, "positive": ["14", 0], "negative": ["15", 0], "vae": ["12", 2], "latent": ["13", 0], "image": ["19", 0]}, "class_type": "LTXVAddGuide", "_meta": {"title": "LTXVAddGuide"}}, "27": {"inputs": {"frame_idx": 120, "strength": 1, "positive": ["26", 0], "negative": ["26", 1], "vae": ["12", 2], "latent": ["26", 2], "image": ["20", 0]}, "class_type": "LTXVAddGuide", "_meta": {"title": "LTXVAddGuide"}}, "31": {"inputs": {"steps": 20, "max_shift": 2.05, "base_shift": 0.95, "stretch": true, "terminal": 0.1, "latent": ["27", 2]}, "class_type": "LTXVScheduler", "_meta": {"title": "LTXVScheduler"}}, "34": {"inputs": {"width": 768, "height": 512, "upscale_method": "nearest-exact", "keep_proportion": false, "divisible_by": 2, "crop": "center", "image": ["2", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "35": {"inputs": {"width": 768, "height": 512, "upscale_method": "nearest-exact", "keep_proportion": false, "divisible_by": 2, "crop": "center", "image": ["1", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "36": {"inputs": {"frame_rate": 25, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "images": ["17", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "37": {"inputs": {"positive": ["10", 0], "negative": ["10", 1], "latent": ["7", 0]}, "class_type": "LTXVCropGuides", "_meta": {"title": "LTXVCropGuides"}}, "38": {"inputs": {"skip_steps_sigma_threshold": 0.996, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "4, 4, 4, 4, 1, 1", "stg_scale_values": "2, 2, 2, 2, 1, 0", "stg_rescale_values": "1, 1, 1, 1, 1, 1", "stg_layers_indices": "[14], [14], [14], [14], [14], [14]", "model": ["12", 0], "positive": ["10", 0], "negative": ["10", 1]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}}, "comfy_fork_version": "feature/memory_prof@f7e64946"}, "version": 0.4}