{"id": "9537f623-73b7-4225-b4c0-f0bd2547d07f", "revision": 0, "last_node_id": 95, "last_link_id": 239, "nodes": [{"id": 81, "type": "SamplerCustomAdvanced", "pos": [1199.************, 17.57975959777832], "size": [355.20001220703125, 106], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "noise", "name": "noise", "type": "NOISE", "link": 194}, {"localized_name": "guider", "name": "guider", "type": "GUIDER", "link": 238}, {"localized_name": "sampler", "name": "sampler", "type": "SAMPLER", "link": 196}, {"localized_name": "sigmas", "name": "sigmas", "type": "SIGMAS", "link": 197}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 234}], "outputs": [{"localized_name": "output", "name": "output", "type": "LATENT", "slot_index": 0, "links": [199]}, {"localized_name": "denoised_output", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": []}, {"id": 71, "type": "LTXVScheduler", "pos": [1225.6614990234375, 396.2705993652344], "size": [315, 154], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 233}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "max_shift", "name": "max_shift", "type": "FLOAT", "widget": {"name": "max_shift"}, "link": null}, {"localized_name": "base_shift", "name": "base_shift", "type": "FLOAT", "widget": {"name": "base_shift"}, "link": null}, {"localized_name": "stretch", "name": "stretch", "type": "BOOLEAN", "widget": {"name": "stretch"}, "link": null}, {"localized_name": "terminal", "name": "terminal", "type": "FLOAT", "widget": {"name": "terminal"}, "link": null}], "outputs": [{"localized_name": "SIGMAS", "name": "SIGMAS", "type": "SIGMAS", "slot_index": 0, "links": [197]}], "properties": {"Node name for S&R": "LTXVScheduler", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": [20, 2.05, 0.95, true, 0.1]}, {"id": 8, "type": "VAEDecode", "pos": [1600, 30], "size": [210, 46], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 199}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 212}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [211]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": []}, {"id": 73, "type": "KSamplerSelect", "pos": [1213.25390625, 229.91848754882812], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"localized_name": "SAMPLER", "name": "SAMPLER", "type": "SAMPLER", "slot_index": 0, "links": [196]}], "properties": {"Node name for S&R": "KSamplerSelect", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["gradient_estimation"]}, {"id": 88, "type": "VHS_VideoCombine", "pos": [1584.6419677734375, 152.86083984375], "size": [315, 520.6666870117188], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 211}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 25, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv_00262.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 25}}}}, {"id": 85, "type": "LTXVPromptEnhancer", "pos": [49.36366653442383, 359.4747619628906], "size": [340.20001220703125, 102], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "prompt_enhancer", "name": "prompt_enhancer", "type": "LTXV_PROMPT_ENHANCER", "link": 207}, {"localized_name": "image_prompt", "name": "image_prompt", "shape": 7, "type": "IMAGE", "link": 205}, {"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "max_resulting_tokens", "name": "max_resulting_tokens", "type": "INT", "widget": {"name": "max_resulting_tokens"}, "link": null}], "outputs": [{"localized_name": "str", "name": "str", "type": "STRING", "slot_index": 0, "links": [206]}], "properties": {"Node name for S&R": "LTXVPromptEnhancer"}, "widgets_values": ["a person looking like <PERSON><PERSON><PERSON> talking", 256]}, {"id": 83, "type": "RandomNoise", "pos": [453.44482421875, 47.46747970581055], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"localized_name": "NOISE", "name": "NOISE", "type": "NOISE", "slot_index": 0, "links": [194]}], "properties": {"Node name for S&R": "RandomNoise", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": [42, "fixed"]}, {"id": 69, "type": "LTXVConditioning", "pos": [885.63818359375, 226.95408630371094], "size": [223.8660125732422, 78], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 231}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 232}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [236]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [237]}], "properties": {"Node name for S&R": "LTXVConditioning", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": [25]}, {"id": 78, "type": "LoadImage", "pos": [-1.540905237197876, 542.3555908203125], "size": [385.15606689453125, 333.3305358886719], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "choose file to upload", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [205, 227]}, {"localized_name": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["shrek2.jpg", "image"]}, {"id": 93, "type": "LTXVImgToVideoAdvanced", "pos": [844.2299194335938, 354.78912353515625], "size": [350.8185729980469, 310], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 228}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 229}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 230}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 227}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}, {"localized_name": "crf", "name": "crf", "type": "INT", "widget": {"name": "crf"}, "link": null}, {"localized_name": "blur_radius", "name": "blur_radius", "type": "INT", "widget": {"name": "blur_radius"}, "link": null}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "crop", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": [231]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [232]}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "links": [233, 234]}], "properties": {"Node name for S&R": "LTXVImgToVideoAdvanced"}, "widgets_values": [768, 512, 97, 1, 29, 0, "lanc<PERSON>s", "disabled", 0.9]}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [82.3931884765625, -72.30274200439453], "size": [315, 98], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [235]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": null}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [212, 230]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["ltx-video-13b-step-1358000.safetensors"]}, {"id": 95, "type": "STGAdvancedPresets", "pos": [441.8043518066406, -71.08512878417969], "size": [340.20001220703125, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "preset", "name": "preset", "type": "COMBO", "widget": {"name": "preset"}, "link": null}], "outputs": [{"localized_name": "STG_ADVANCED_PRESET", "name": "STG_ADVANCED_PRESET", "type": "STG_ADVANCED_PRESET", "links": [239]}], "properties": {"Node name for S&R": "STGAdvancedPresets"}, "widgets_values": ["13b Balanced"]}, {"id": 38, "type": "CLIPLoader", "pos": [79.50933074951172, 78.87077331542969], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "clip_name", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 86, "type": "LTXVPromptEnhancerLoader", "pos": [-35.292274475097656, 224.54818725585938], "size": [428.4000244140625, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "llm_name", "name": "llm_name", "type": "STRING", "widget": {"name": "llm_name"}, "link": null}, {"localized_name": "image_captioner_name", "name": "image_captioner_name", "type": "STRING", "widget": {"name": "image_captioner_name"}, "link": null}], "outputs": [{"localized_name": "prompt_enhancer", "name": "prompt_enhancer", "type": "LTXV_PROMPT_ENHANCER", "slot_index": 0, "links": [207]}], "properties": {"Node name for S&R": "LTXVPromptEnhancerLoader"}, "widgets_values": ["unsloth/Llama-3.2-3B-Instruct", "MiaoshouAI/Florence-2-large-PromptGen-v2.0"]}, {"id": 94, "type": "STGGuiderAdvanced", "pos": [802.5442504882812, -93.17659759521484], "size": [364.3406066894531, 262], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 235}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 236}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 237}, {"localized_name": "preset", "name": "preset", "shape": 7, "type": "STG_ADVANCED_PRESET", "link": 239}, {"localized_name": "skip_steps_sigma_threshold", "name": "skip_steps_sigma_threshold", "type": "FLOAT", "widget": {"name": "skip_steps_sigma_threshold"}, "link": null}, {"localized_name": "cfg_star_rescale", "name": "cfg_star_rescale", "type": "BOOLEAN", "widget": {"name": "cfg_star_rescale"}, "link": null}, {"localized_name": "sigmas", "name": "sigmas", "type": "STRING", "widget": {"name": "sigmas"}, "link": null}, {"localized_name": "cfg_values", "name": "cfg_values", "type": "STRING", "widget": {"name": "cfg_values"}, "link": null}, {"localized_name": "stg_scale_values", "name": "stg_scale_values", "type": "STRING", "widget": {"name": "stg_scale_values"}, "link": null}, {"localized_name": "stg_rescale_values", "name": "stg_rescale_values", "type": "STRING", "widget": {"name": "stg_rescale_values"}, "link": null}, {"localized_name": "stg_layers_indices", "name": "stg_layers_indices", "type": "STRING", "widget": {"name": "stg_layers_indices"}, "link": null}], "outputs": [{"localized_name": "GUIDER", "name": "GUIDER", "type": "GUIDER", "links": [238]}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.998, true, "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "8, 6, 6, 4, 3, 1", "4, 4, 3, 2, 1, 0", "1, 1, 1, 1, 1, 1", "[29], [29], [29], [29], [29], [29]"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [462.1104736328125, 192.36297607421875], "size": [314.9385986328125, 122.6602783203125], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 74}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 206}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [228]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["best quality, 4k, HDR, a tracking shot of a beautiful scene of the sea waves on the beach with a massive explosion in the water"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [447.7472229003906, 264.065673828125], "size": [335.269775390625, 137.85621643066406], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 75}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [229]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.28"}, "widgets_values": ["low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 90, "type": "Note", "pos": [850.818115234375, 716.1011962890625], "size": [346.115478515625, 88.66456604003906], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["The conditioning image size should match the aspect ratio of the generated video."], "color": "#432", "bgcolor": "#653"}], "links": [[74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [194, 83, 0, 81, 0, "NOISE"], [196, 73, 0, 81, 2, "SAMPLER"], [197, 71, 0, 81, 3, "SIGMAS"], [199, 81, 0, 8, 0, "LATENT"], [205, 78, 0, 85, 1, "IMAGE"], [206, 85, 0, 6, 1, "STRING"], [207, 86, 0, 85, 0, "LTXV_PROMPT_ENHANCER"], [211, 8, 0, 88, 0, "IMAGE"], [212, 44, 2, 8, 1, "VAE"], [227, 78, 0, 93, 3, "IMAGE"], [228, 6, 0, 93, 0, "CONDITIONING"], [229, 7, 0, 93, 1, "CONDITIONING"], [230, 44, 2, 93, 2, "VAE"], [231, 93, 0, 69, 0, "CONDITIONING"], [232, 93, 1, 69, 1, "CONDITIONING"], [233, 93, 2, 71, 0, "LATENT"], [234, 93, 2, 81, 4, "LATENT"], [235, 44, 0, 94, 0, "MODEL"], [236, 69, 0, 94, 1, "CONDITIONING"], [237, 69, 1, 94, 2, "CONDITIONING"], [238, 94, 0, 81, 1, "GUIDER"], [239, 95, 0, 94, 3, "STG_ADVANCED_PRESET"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.7236929081080979, "offset": [230.40779451487305, 202.31611891621958]}, "frontendVersion": "1.17.11", "node_versions": {"comfy-core": "0.3.30"}, "prompt": {"6": {"inputs": {"text": ["85", 0], "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["81", 0], "vae": ["44", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "38": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "44": {"inputs": {"ckpt_name": "ltx-video-13b-step-1358000.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "69": {"inputs": {"frame_rate": 25, "positive": ["93", 0], "negative": ["93", 1]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "71": {"inputs": {"steps": 20, "max_shift": 2.05, "base_shift": 0.95, "stretch": true, "terminal": 0.1, "latent": ["93", 2]}, "class_type": "LTXVScheduler", "_meta": {"title": "LTXVScheduler"}}, "73": {"inputs": {"sampler_name": "gradient_estimation"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "78": {"inputs": {"image": "shrek2.jpg"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "81": {"inputs": {"noise": ["83", 0], "guider": ["94", 0], "sampler": ["73", 0], "sigmas": ["71", 0], "latent_image": ["93", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "83": {"inputs": {"noise_seed": 42}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "85": {"inputs": {"prompt": "a person looking like <PERSON><PERSON><PERSON> talking", "max_resulting_tokens": 256, "prompt_enhancer": ["86", 0], "image_prompt": ["78", 0]}, "class_type": "LTXVPromptEnhancer", "_meta": {"title": "🅛🅣🅧 LTXV Prompt Enhancer"}}, "86": {"inputs": {"llm_name": "unsloth/Llama-3.2-3B-Instruct", "image_captioner_name": "MiaoshouAI/Florence-2-large-PromptGen-v2.0"}, "class_type": "LTXVPromptEnhancerLoader", "_meta": {"title": "🅛🅣🅧 LTXV Prompt Enhancer Loader"}}, "88": {"inputs": {"frame_rate": 25, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "images": ["8", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "93": {"inputs": {"width": 768, "height": 512, "length": 97, "batch_size": 1, "crf": 29, "blur_radius": 0, "interpolation": "lanc<PERSON>s", "crop": "disabled", "strength": 0.9, "positive": ["6", 0], "negative": ["7", 0], "vae": ["44", 2], "image": ["78", 0]}, "class_type": "LTXVImgToVideoAdvanced", "_meta": {"title": "🅛🅣🅧 LTXV Img To Video Advanced"}}, "94": {"inputs": {"skip_steps_sigma_threshold": 0.998, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "8, 6, 6, 4, 3, 1", "stg_scale_values": "4, 4, 3, 2, 1, 0", "stg_rescale_values": "1, 1, 1, 1, 1, 1", "stg_layers_indices": "[29], [29], [29], [29], [29], [29]", "model": ["44", 0], "positive": ["69", 0], "negative": ["69", 1], "preset": ["95", 0]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "95": {"inputs": {"preset": "13b Balanced"}, "class_type": "STGAdvancedPresets", "_meta": {"title": "🅛🅣🅧 STG Advanced Presets"}}}, "comfy_fork_version": "develop@311c9b81"}, "version": 0.4}