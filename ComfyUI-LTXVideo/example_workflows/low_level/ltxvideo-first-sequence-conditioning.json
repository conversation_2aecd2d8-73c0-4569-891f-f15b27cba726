{"id": "cc2b03f6-2870-4524-85bb-e3ea9a4c6620", "revision": 0, "last_node_id": 215, "last_link_id": 595, "nodes": [{"id": 69, "type": "LTXVConditioning", "pos": [505.2907409667969, 2.978607177734375], "size": [223.8660125732422, 78], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 266}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 267}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [589]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [588]}], "properties": {"Node name for S&R": "LTXVConditioning", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": [25]}, {"id": 199, "type": "LTXVPromptEnhancerLoader", "pos": [-394.3362121582031, 138.89170837402344], "size": [428.4000244140625, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "llm_name", "name": "llm_name", "type": "STRING", "widget": {"name": "llm_name"}, "link": null}, {"localized_name": "image_captioner_name", "name": "image_captioner_name", "type": "STRING", "widget": {"name": "image_captioner_name"}, "link": null}], "outputs": [{"localized_name": "prompt_enhancer", "name": "prompt_enhancer", "type": "LTXV_PROMPT_ENHANCER", "slot_index": 0, "links": [538]}], "properties": {"Node name for S&R": "LTXVPromptEnhancerLoader", "aux_id": "Lightricks/ComfyUI-LTXVideo", "ver": "55f2d9ebb885a1bda63f1757080b4d7f8f7bb2f9"}, "widgets_values": ["unsloth/Llama-3.2-3B-Instruct", "MiaoshouAI/Florence-2-large-PromptGen-v2.0"]}, {"id": 38, "type": "CLIPLoader", "pos": [-295.5227966308594, -3.089735269546509], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "clip_name", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [102.96157836914062, -5.444018840789795], "size": [422.84503173828125, 164.31304931640625], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 74}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 507}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [266]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": ["A confident man with short dark hair and a beard, wearing a casual green jacket, sits at a chessboard with a smile. He leans forward, hands near the pieces, as if about to make a move. The background shows a dimly lit hall with a blurred audience watching, creating a tense yet exciting atmosphere. The video should capture subtle movements like eye shifts, hand gestures, and slight changes in facial expression to enhance realism."], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [100.65459442138672, 52.6732177734375], "size": [425.27801513671875, 180.6060791015625], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 75}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [267]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": ["low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 186, "type": "LTXVPromptEnhancer", "pos": [64.0250473022461, 108.81405639648438], "size": [340.20001220703125, 102], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "prompt_enhancer", "name": "prompt_enhancer", "type": "LTXV_PROMPT_ENHANCER", "link": 538}, {"localized_name": "image_prompt", "name": "image_prompt", "shape": 7, "type": "IMAGE", "link": 523}, {"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "max_resulting_tokens", "name": "max_resulting_tokens", "type": "INT", "widget": {"name": "max_resulting_tokens"}, "link": null}], "outputs": [{"localized_name": "str", "name": "str", "type": "STRING", "slot_index": 0, "links": [507]}], "properties": {"Node name for S&R": "LTXVPromptEnhancer", "aux_id": "Lightricks/ComfyUI-LTXVideo", "ver": "55f2d9ebb885a1bda63f1757080b4d7f8f7bb2f9"}, "widgets_values": ["red jeep", 256]}, {"id": 209, "type": "GetImageSizeAndCount", "pos": [97.28514862060547, 266.0632019042969], "size": [277.20001220703125, 86], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 567}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"localized_name": "width", "name": "768 width", "type": "INT", "slot_index": 1, "links": [568]}, {"localized_name": "height", "name": "512 height", "type": "INT", "slot_index": 2, "links": [569]}, {"localized_name": "count", "name": "65 count", "type": "INT", "slot_index": 3, "links": [570]}], "properties": {"Node name for S&R": "GetImageSizeAndCount", "cnr_id": "comfyui-kjnodes", "ver": "fa6d20eeb32cbcbd84ee325e3cfdc6ed6b43056f"}, "widgets_values": []}, {"id": 192, "type": "VHS_SelectImages", "pos": [82.86734008789062, 410.3556213378906], "size": [315, 106], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 525}, {"localized_name": "indexes", "name": "indexes", "type": "STRING", "widget": {"name": "indexes"}, "link": null}, {"localized_name": "err_if_missing", "name": "err_if_missing", "type": "BOOLEAN", "widget": {"name": "err_if_missing"}, "link": null}, {"localized_name": "err_if_empty", "name": "err_if_empty", "type": "BOOLEAN", "widget": {"name": "err_if_empty"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [571]}], "properties": {"Node name for S&R": "VHS_SelectImages", "cnr_id": "comfyui-videohelpersuite", "ver": "8629188458dc6cb832f871ece3bd273507e8a766"}, "widgets_values": {"indexes": "-9:", "err_if_missing": true, "err_if_empty": true}}, {"id": 207, "type": "Note", "pos": [92.88219451904297, 576.026611328125], "size": [299.7644958496094, 93.54439544677734], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Selects the last 9 frames and sends them as a conditioning sequence."], "color": "#432", "bgcolor": "#653"}, {"id": 80, "type": "EmptyLTXVLatentVideo", "pos": [461.7226867675781, 130.12966918945312], "size": [315, 170], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 568}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 569}, {"localized_name": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 570}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [586]}], "properties": {"Node name for S&R": "EmptyLTXVLatentVideo", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": [768, 512, 129, 1]}, {"id": 210, "type": "LTXVPreprocess", "pos": [459.7139587402344, 306.710693359375], "size": [315, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 571}, {"localized_name": "img_compression", "name": "img_compression", "type": "INT", "widget": {"name": "img_compression"}, "link": null}], "outputs": [{"localized_name": "output_image", "name": "output_image", "type": "IMAGE", "slot_index": 0, "links": [585]}], "properties": {"Node name for S&R": "LTXVPreprocess", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": [35]}, {"id": 194, "type": "VHS_SelectImages", "pos": [457.54083251953125, 428.50592041015625], "size": [315, 106], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 527}, {"localized_name": "indexes", "name": "indexes", "type": "STRING", "widget": {"name": "indexes"}, "link": null}, {"localized_name": "err_if_missing", "name": "err_if_missing", "type": "BOOLEAN", "widget": {"name": "err_if_missing"}, "link": null}, {"localized_name": "err_if_empty", "name": "err_if_empty", "type": "BOOLEAN", "widget": {"name": "err_if_empty"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [537]}], "properties": {"Node name for S&R": "VHS_SelectImages", "cnr_id": "comfyui-videohelpersuite", "ver": "8629188458dc6cb832f871ece3bd273507e8a766"}, "widgets_values": {"indexes": ":-9", "err_if_missing": true, "err_if_empty": true}}, {"id": 208, "type": "Note", "pos": [462.1504821777344, 579.************], "size": [299.7644958496094, 93.54439544677734], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Selects the rest of the video and attaches the generated video to it."], "color": "#432", "bgcolor": "#653"}, {"id": 213, "type": "STGGuiderAdvanced", "pos": [1220.************, -132.0933074951172], "size": [327.5999755859375, 262], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 580}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 594}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 595}, {"localized_name": "preset", "name": "preset", "shape": 7, "type": "STG_ADVANCED_PRESET", "link": 584}, {"localized_name": "skip_steps_sigma_threshold", "name": "skip_steps_sigma_threshold", "type": "FLOAT", "widget": {"name": "skip_steps_sigma_threshold"}, "link": null}, {"localized_name": "cfg_star_rescale", "name": "cfg_star_rescale", "type": "BOOLEAN", "widget": {"name": "cfg_star_rescale"}, "link": null}, {"localized_name": "sigmas", "name": "sigmas", "type": "STRING", "widget": {"name": "sigmas"}, "link": null}, {"localized_name": "cfg_values", "name": "cfg_values", "type": "STRING", "widget": {"name": "cfg_values"}, "link": null}, {"localized_name": "stg_scale_values", "name": "stg_scale_values", "type": "STRING", "widget": {"name": "stg_scale_values"}, "link": null}, {"localized_name": "stg_rescale_values", "name": "stg_rescale_values", "type": "STRING", "widget": {"name": "stg_rescale_values"}, "link": null}, {"localized_name": "stg_layers_indices", "name": "stg_layers_indices", "type": "STRING", "widget": {"name": "stg_layers_indices"}, "link": null}], "outputs": [{"localized_name": "GUIDER", "name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [583]}], "properties": {"Node name for S&R": "STGGuiderAdvanced"}, "widgets_values": [0.996, true, "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "4, 4, 4, 4, 1, 1", "2, 2, 2, 2, 1, 0", "1, 1, 1, 1, 1, 1", "[14], [14], [14], [14], [14], [14]"]}, {"id": 212, "type": "LTXVCropGuides", "pos": [1254.9326171875, 344.5459289550781], "size": [216.59999084472656, 66], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 590}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 591}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "link": 578}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": null}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": null}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "slot_index": 2, "links": [579]}], "properties": {"Node name for S&R": "LTXVCropGuides", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": []}, {"id": 8, "type": "VAEDecode", "pos": [1255.8253173828125, 468.36798095703125], "size": [210, 46], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 579}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 565}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [536]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": []}, {"id": 193, "type": "ImageBatch", "pos": [1613.5460205078125, -128.23391723632812], "size": [210, 46], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "image1", "name": "image1", "type": "IMAGE", "link": 537}, {"localized_name": "image2", "name": "image2", "type": "IMAGE", "link": 536}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [530]}], "properties": {"Node name for S&R": "ImageBatch", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": []}, {"id": 195, "type": "VHS_VideoCombine", "pos": [1602.831298828125, -6.919589996337891], "size": [315, 520.6666870117188], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 530}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "8629188458dc6cb832f871ece3bd273507e8a766"}, "widgets_values": {"frame_rate": 25, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltxv_00268.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 25, "workflow": "ltxv_00032.png", "fullpath": "/home/<USER>/ComfyUI-Orig/output/ltxv_00032.mp4"}}}}, {"id": 200, "type": "SamplerCustomAdvanced", "pos": [1223.8555908203125, 171.28802490234375], "size": [326.99456787109375, 106], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "noise", "name": "noise", "type": "NOISE", "link": 540}, {"localized_name": "guider", "name": "guider", "type": "GUIDER", "link": 583}, {"localized_name": "sampler", "name": "sampler", "type": "SAMPLER", "link": 541}, {"localized_name": "sigmas", "name": "sigmas", "type": "SIGMAS", "link": 542}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 592}], "outputs": [{"localized_name": "output", "name": "output", "type": "LATENT", "slot_index": 0, "links": [578]}, {"localized_name": "denoised_output", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": []}, {"id": 190, "type": "RandomNoise", "pos": [409.034423828125, -134.0706024169922], "size": [315, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"localized_name": "NOISE", "name": "NOISE", "type": "NOISE", "slot_index": 0, "links": [540]}], "properties": {"Node name for S&R": "RandomNoise", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": [43, "fixed"]}, {"id": 73, "type": "KSamplerSelect", "pos": [829.0675659179688, 562.8773803710938], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"localized_name": "SAMPLER", "name": "SAMPLER", "type": "SAMPLER", "slot_index": 0, "links": [541]}], "properties": {"Node name for S&R": "KSamplerSelect", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": ["gradient_estimation"]}, {"id": 71, "type": "LTXVScheduler", "pos": [827.3433837890625, 348.0218505859375], "size": [315, 154], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 593}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "max_shift", "name": "max_shift", "type": "FLOAT", "widget": {"name": "max_shift"}, "link": null}, {"localized_name": "base_shift", "name": "base_shift", "type": "FLOAT", "widget": {"name": "base_shift"}, "link": null}, {"localized_name": "stretch", "name": "stretch", "type": "BOOLEAN", "widget": {"name": "stretch"}, "link": null}, {"localized_name": "terminal", "name": "terminal", "type": "FLOAT", "widget": {"name": "terminal"}, "link": null}], "outputs": [{"localized_name": "SIGMAS", "name": "SIGMAS", "type": "SIGMAS", "slot_index": 0, "links": [542]}], "properties": {"Node name for S&R": "LTXVScheduler", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": [20, 2.05, 0.95, true, 0.1]}, {"id": 215, "type": "LTXVAddGuideAdvanced", "pos": [815.1484375, -7.902004718780518], "size": [336.9648132324219, 258], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 589}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 588}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 587}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "link": 586}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 585}, {"localized_name": "frame_idx", "name": "frame_idx", "type": "INT", "widget": {"name": "frame_idx"}, "link": null}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "crf", "name": "crf", "type": "INT", "widget": {"name": "crf"}, "link": null}, {"localized_name": "blur_radius", "name": "blur_radius", "type": "INT", "widget": {"name": "blur_radius"}, "link": null}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "crop", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": [590, 594]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [591, 595]}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "links": [592, 593]}], "properties": {"Node name for S&R": "LTXVAddGuideAdvanced"}, "widgets_values": [0, 1, 29, 0, "lanc<PERSON>s", "disabled"]}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [-301.************, -148.44154357910156], "size": [315, 98], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [580]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": null}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [565, 587]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.19"}, "widgets_values": ["ltx-video-13b-step-1358000.safetensors"]}, {"id": 214, "type": "STGAdvancedPresets", "pos": [809.1151733398438, -117.02359008789062], "size": [340.20001220703125, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "preset", "name": "preset", "type": "COMBO", "widget": {"name": "preset"}, "link": null}], "outputs": [{"localized_name": "STG_ADVANCED_PRESET", "name": "STG_ADVANCED_PRESET", "type": "STG_ADVANCED_PRESET", "links": [584]}], "properties": {"Node name for S&R": "STGAdvancedPresets"}, "widgets_values": ["13b Balanced"]}, {"id": 134, "type": "VHS_LoadVideo", "pos": [-256.13458251953125, 277.4459533691406], "size": [252.056640625, 430.7044372558594], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "video", "name": "video", "type": "COMBO", "widget": {"name": "video"}, "link": null}, {"localized_name": "force_rate", "name": "force_rate", "type": "INT", "widget": {"name": "force_rate"}, "link": null}, {"localized_name": "force_size", "name": "force_size", "type": "COMBO", "widget": {"name": "force_size"}, "link": null}, {"localized_name": "custom_width", "name": "custom_width", "type": "INT", "widget": {"name": "custom_width"}, "link": null}, {"localized_name": "custom_height", "name": "custom_height", "type": "INT", "widget": {"name": "custom_height"}, "link": null}, {"localized_name": "frame_load_cap", "name": "frame_load_cap", "type": "INT", "widget": {"name": "frame_load_cap"}, "link": null}, {"localized_name": "skip_first_frames", "name": "skip_first_frames", "type": "INT", "widget": {"name": "skip_first_frames"}, "link": null}, {"localized_name": "select_every_nth", "name": "select_every_nth", "type": "INT", "widget": {"name": "select_every_nth"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [523, 525, 527, 567]}, {"localized_name": "frame_count", "name": "frame_count", "type": "INT", "links": null}, {"localized_name": "audio", "name": "audio", "type": "AUDIO", "links": null}, {"localized_name": "video_info", "name": "video_info", "type": "VHS_VIDEOINFO", "links": null}], "properties": {"Node name for S&R": "VHS_LoadVideo", "cnr_id": "comfyui-videohelpersuite", "ver": "8629188458dc6cb832f871ece3bd273507e8a766"}, "widgets_values": {"video": "jeep.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 0, "custom_height": 0, "frame_load_cap": 65, "skip_first_frames": 0, "select_every_nth": 1, "choose video to upload": "image", "videopreview": {"hidden": false, "paused": false, "params": {"frame_load_cap": 65, "skip_first_frames": 0, "force_rate": 0, "filename": "jeep.mp4", "type": "input", "format": "video/mp4", "select_every_nth": 1}}}}], "links": [[74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [266, 6, 0, 69, 0, "CONDITIONING"], [267, 7, 0, 69, 1, "CONDITIONING"], [507, 186, 0, 6, 1, "STRING"], [523, 134, 0, 186, 1, "IMAGE"], [525, 134, 0, 192, 0, "IMAGE"], [527, 134, 0, 194, 0, "IMAGE"], [530, 193, 0, 195, 0, "IMAGE"], [536, 8, 0, 193, 1, "IMAGE"], [537, 194, 0, 193, 0, "IMAGE"], [538, 199, 0, 186, 0, "LTXV_PROMPT_ENHANCER"], [540, 190, 0, 200, 0, "NOISE"], [541, 73, 0, 200, 2, "SAMPLER"], [542, 71, 0, 200, 3, "SIGMAS"], [565, 44, 2, 8, 1, "VAE"], [567, 134, 0, 209, 0, "IMAGE"], [568, 209, 1, 80, 0, "INT"], [569, 209, 2, 80, 1, "INT"], [570, 209, 3, 80, 2, "INT"], [571, 192, 0, 210, 0, "IMAGE"], [578, 200, 0, 212, 2, "LATENT"], [579, 212, 2, 8, 0, "LATENT"], [580, 44, 0, 213, 0, "MODEL"], [583, 213, 0, 200, 1, "GUIDER"], [584, 214, 0, 213, 3, "STG_ADVANCED_PRESET"], [585, 210, 0, 215, 4, "IMAGE"], [586, 80, 0, 215, 3, "LATENT"], [587, 44, 2, 215, 2, "VAE"], [588, 69, 1, 215, 1, "CONDITIONING"], [589, 69, 0, 215, 0, "CONDITIONING"], [590, 215, 0, 212, 0, "CONDITIONING"], [591, 215, 1, 212, 1, "CONDITIONING"], [592, 215, 2, 200, 4, "LATENT"], [593, 215, 2, 71, 0, "LATENT"], [594, 215, 0, 213, 1, "CONDITIONING"], [595, 215, 1, 213, 2, "CONDITIONING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6475006588876424, "offset": [447.67641902101894, 314.6585002856935]}, "prompt": {"6": {"inputs": {"text": ["186", 0], "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["212", 2], "vae": ["44", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "38": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "44": {"inputs": {"ckpt_name": "ltx-video-13b-step-1358000.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "69": {"inputs": {"frame_rate": 25, "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "71": {"inputs": {"steps": 20, "max_shift": 2.05, "base_shift": 0.95, "stretch": true, "terminal": 0.1, "latent": ["215", 2]}, "class_type": "LTXVScheduler", "_meta": {"title": "LTXVScheduler"}}, "73": {"inputs": {"sampler_name": "gradient_estimation"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "80": {"inputs": {"width": ["209", 1], "height": ["209", 2], "length": ["209", 3], "batch_size": 1}, "class_type": "EmptyLTXVLatentVideo", "_meta": {"title": "EmptyLTXVLatentVideo"}}, "134": {"inputs": {"video": "jeep.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 0, "custom_height": 0, "frame_load_cap": 65, "skip_first_frames": 0, "select_every_nth": 1}, "class_type": "VHS_LoadVideo", "_meta": {"title": "Load Video (Upload) 🎥🅥🅗🅢"}}, "186": {"inputs": {"prompt": "red jeep", "max_resulting_tokens": 256, "prompt_enhancer": ["199", 0], "image_prompt": ["134", 0]}, "class_type": "LTXVPromptEnhancer", "_meta": {"title": "🅛🅣🅧 LTXV Prompt Enhancer"}}, "190": {"inputs": {"noise_seed": 43}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "192": {"inputs": {"indexes": "-9:", "err_if_missing": true, "err_if_empty": true, "image": ["134", 0]}, "class_type": "VHS_SelectImages", "_meta": {"title": "Select Images 🎥🅥🅗🅢"}}, "193": {"inputs": {"image1": ["194", 0], "image2": ["8", 0]}, "class_type": "ImageBatch", "_meta": {"title": "Batch Images"}}, "194": {"inputs": {"indexes": ":-9", "err_if_missing": true, "err_if_empty": true, "image": ["134", 0]}, "class_type": "VHS_SelectImages", "_meta": {"title": "Select Images 🎥🅥🅗🅢"}}, "195": {"inputs": {"frame_rate": 25, "loop_count": 0, "filename_prefix": "ltxv", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "images": ["193", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "199": {"inputs": {"llm_name": "unsloth/Llama-3.2-3B-Instruct", "image_captioner_name": "MiaoshouAI/Florence-2-large-PromptGen-v2.0"}, "class_type": "LTXVPromptEnhancerLoader", "_meta": {"title": "🅛🅣🅧 LTXV Prompt Enhancer Loader"}}, "200": {"inputs": {"noise": ["190", 0], "guider": ["213", 0], "sampler": ["73", 0], "sigmas": ["71", 0], "latent_image": ["215", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "209": {"inputs": {"image": ["134", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "210": {"inputs": {"img_compression": 35, "image": ["192", 0]}, "class_type": "LTXVPreprocess", "_meta": {"title": "LTXVPreprocess"}}, "212": {"inputs": {"positive": ["215", 0], "negative": ["215", 1], "latent": ["200", 0]}, "class_type": "LTXVCropGuides", "_meta": {"title": "LTXVCropGuides"}}, "213": {"inputs": {"skip_steps_sigma_threshold": 0.996, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "4, 4, 4, 4, 1, 1", "stg_scale_values": "2, 2, 2, 2, 1, 0", "stg_rescale_values": "1, 1, 1, 1, 1, 1", "stg_layers_indices": "[14], [14], [14], [14], [14], [14]", "model": ["44", 0], "positive": ["215", 0], "negative": ["215", 1], "preset": ["214", 0]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "214": {"inputs": {"preset": "13b Balanced"}, "class_type": "STGAdvancedPresets", "_meta": {"title": "🅛🅣🅧 STG Advanced Presets"}}, "215": {"inputs": {"frame_idx": 0, "strength": 1, "crf": 29, "blur_radius": 0, "interpolation": "lanc<PERSON>s", "crop": "disabled", "positive": ["69", 0], "negative": ["69", 1], "vae": ["44", 2], "latent": ["80", 0], "image": ["210", 0]}, "class_type": "LTXVAddGuideAdvanced", "_meta": {"title": "🅛🅣🅧 LTXV Add Guide Advanced"}}}, "comfy_fork_version": "develop@311c9b81", "node_versions": {"comfy-core": "0.3.30", "comfyui-kjnodes": "1.0.8"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "frontendVersion": "1.17.11"}, "version": 0.4}