{"last_node_id": 352, "last_link_id": 744, "nodes": [{"id": 267, "type": "SetNode", "pos": [45.03347396850586, 1518.7252197265625], "size": [210, 58], "flags": {"collapsed": true}, "order": 29, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 525}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_HEIGHT", "properties": {"previousName": "HEIGHT"}, "widgets_values": ["HEIGHT"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 266, "type": "SetNode", "pos": [45.03347396850586, 1468.7252197265625], "size": [210, 58], "flags": {"collapsed": true}, "order": 28, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 524}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_WIDTH", "properties": {"previousName": "WIDTH"}, "widgets_values": ["WIDTH"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 133, "type": "SetNode", "pos": [35.033485412597656, 1418.7252197265625], "size": [210, 58], "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 418}], "outputs": [{"name": "*", "type": "*", "links": [], "slot_index": 0}], "title": "Set_INPUT_VID", "properties": {"previousName": "INPUT_VID"}, "widgets_values": ["INPUT_VID"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 120, "type": "GetNode", "pos": [272.622314453125, 973.639404296875], "size": [210, 58], "flags": {"collapsed": false}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [259, 260, 587, 588], "slot_index": 0}], "title": "Get_CLIP", "properties": {}, "widgets_values": ["CLIP"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [645.3626098632812, 998.8179931640625], "size": [285.6000061035156, 114.15338134765625], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 260}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [193], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["shaky, glitchy, low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 316, "type": "LTXReverseModelSamplingPred", "pos": [1640, 1040], "size": [277.20001220703125, 26], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 616}], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [613], "slot_index": 0}], "properties": {"Node name for S&R": "LTXReverseModelSamplingPred"}, "widgets_values": []}, {"id": 305, "type": "SamplerCustomAdvanced", "pos": [1650, 1430], "size": [236.8000030517578, 106], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "noise", "localized_name": "noise", "type": "NOISE", "link": 581}, {"name": "guider", "localized_name": "guider", "type": "GUIDER", "link": 614}, {"name": "sampler", "localized_name": "sampler", "type": "SAMPLER", "link": 608}, {"name": "sigmas", "localized_name": "sigmas", "type": "SIGMAS", "link": 583}, {"name": "latent_image", "localized_name": "latent_image", "type": "LATENT", "link": 738}], "outputs": [{"name": "output", "localized_name": "output", "type": "LATENT", "links": [739], "slot_index": 0}, {"name": "denoised_output", "localized_name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 308, "type": "<PERSON><PERSON><PERSON><PERSON>", "pos": [1650, 1350], "size": [210, 26], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "localized_name": "NOISE", "type": "NOISE", "links": [581], "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 323, "type": "LTXPerturbedAttention", "pos": [1580, 860], "size": [365.4000244140625, 126], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 615}, {"name": "attn_override", "localized_name": "attn_override", "type": "ATTN_OVERRIDE", "shape": 7, "link": null}], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [616], "slot_index": 0}], "properties": {"Node name for S&R": "LTXPerturbedAttention"}, "widgets_values": [1, 0.75, 5]}, {"id": 311, "type": "CLIPTextEncode", "pos": [653.456298828125, 1399.64404296875], "size": [285.6000061035156, 114.15338134765625], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 588}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [723], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["shaky, glitchy, low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [644.2054443359375, 769.26171875], "size": [477.6527404785156, 183.92239379882812], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 259}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [370], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a 30 year old Israeli man speaking and moving around. The room is dim. The camera is static. The footage semms to come from real world footage. "], "color": "#232", "bgcolor": "#353"}, {"id": 332, "type": "GetImageSizeAndCount", "pos": [337.658935546875, 1532.3609619140625], "size": [277.20001220703125, 86], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 641}], "outputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "links": [642], "slot_index": 0}, {"name": "width", "localized_name": "width", "type": "INT", "links": null}, {"name": "height", "localized_name": "height", "type": "INT", "links": null}, {"name": "count", "localized_name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}, {"id": 322, "type": "LTXFlowEditCFGGuider", "pos": [1630, 1140], "size": [315, 162], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 613}, {"name": "source_pos", "localized_name": "source_pos", "type": "CONDITIONING", "link": 609}, {"name": "source_neg", "localized_name": "source_neg", "type": "CONDITIONING", "link": 610}, {"name": "target_pos", "localized_name": "target_pos", "type": "CONDITIONING", "link": 639}, {"name": "target_neg", "localized_name": "target_neg", "type": "CONDITIONING", "link": 640}], "outputs": [{"name": "GUIDER", "localized_name": "GUIDER", "type": "GUIDER", "links": [614], "slot_index": 0}], "properties": {"Node name for S&R": "LTXFlowEditCFGGuider"}, "widgets_values": [2, 6.5]}, {"id": 84, "type": "ModifyLTXModel", "pos": [-50.0333366394043, 845.7896728515625], "size": [140, 26], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 607}], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [258], "slot_index": 0}], "properties": {"Node name for S&R": "ModifyLTXModel"}, "widgets_values": []}, {"id": 119, "type": "SetNode", "pos": [75.80824279785156, 888.0110473632812], "size": [210, 58], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [{"name": "MODEL", "type": "MODEL", "link": 258}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_MODEL", "properties": {"previousName": "MODEL"}, "widgets_values": ["MODEL"], "color": "#223", "bgcolor": "#335"}, {"id": 38, "type": "CLIPLoader", "pos": [-281.29266357421875, 993.9712524414062], "size": [210, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "localized_name": "CLIP", "type": "CLIP", "links": [542], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 118, "type": "SetNode", "pos": [-278.79730224609375, 1140.1246337890625], "size": [210, 58], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"name": "CLIP", "type": "CLIP", "link": 542}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_CLIP", "properties": {"previousName": "CLIP"}, "widgets_values": ["CLIP"], "color": "#432", "bgcolor": "#653"}, {"id": 90, "type": "SetNode", "pos": [75.1419906616211, 1101.4815673828125], "size": [210, 58], "flags": {"collapsed": true}, "order": 20, "mode": 0, "inputs": [{"name": "VAE", "type": "VAE", "link": 721}], "outputs": [{"name": "*", "type": "*", "links": [], "slot_index": 0}], "title": "Set_VAE", "properties": {"previousName": "VAE"}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 337, "type": "LTXVPreprocess", "pos": [301.02386474609375, 1803.87060546875], "size": [315, 58], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 666}], "outputs": [{"name": "output_image", "localized_name": "output_image", "type": "IMAGE", "links": [726], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVPreprocess"}, "widgets_values": [35]}, {"id": 69, "type": "LTXVConditioning", "pos": [1330, 980], "size": [210, 78], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 370}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 193}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [609], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [610], "slot_index": 1}], "properties": {"Node name for S&R": "LTXVConditioning"}, "widgets_values": [24]}, {"id": 314, "type": "GetNode", "pos": [632.2711181640625, 1703.3206787109375], "size": [210, 58], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [594, 724, 731], "slot_index": 0}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 315, "type": "VAEEncode", "pos": [622.2001953125, 1621.95703125], "size": [210, 46], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "pixels", "localized_name": "pixels", "type": "IMAGE", "link": 642}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 594}], "outputs": [{"name": "LATENT", "localized_name": "LATENT", "type": "LATENT", "links": [725], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 71, "type": "LTXVScheduler", "pos": [1660, 1590], "size": [210, 154], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "latent", "localized_name": "latent", "type": "LATENT", "shape": 7, "link": 737}], "outputs": [{"name": "SIGMAS", "localized_name": "SIGMAS", "type": "SIGMAS", "links": [583], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVScheduler"}, "widgets_values": [30, 2.05, 0.95, true, 0.1]}, {"id": 321, "type": "LTXFlowEditSampler", "pos": [1640, 1790], "size": [259.5262145996094, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "localized_name": "SAMPLER", "type": "SAMPLER", "links": [608], "slot_index": 0}], "properties": {"Node name for S&R": "LTXFlowEditSampler"}, "widgets_values": [3, 6, 0, "fixed"]}, {"id": 310, "type": "CLIPTextEncode", "pos": [649.3215942382812, 1160.6651611328125], "size": [515.0952758789062, 178.36598205566406], "flags": {"collapsed": false}, "order": 15, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 587}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [722], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A character that looks like <PERSON><PERSON><PERSON> from the movie is in a fantasy style room. He standing and speaks. The camera is static. The lighting is natural. The footage seems to come from real world footage."], "color": "#232", "bgcolor": "#353"}, {"id": 194, "type": "ImageResizeKJ", "pos": [-5.374414920806885, 1137.1494140625], "size": [210, 286], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 541}, {"name": "get_image_size", "localized_name": "get_image_size", "type": "IMAGE", "shape": 7, "link": null}, {"name": "width_input", "type": "INT", "shape": 7, "pos": [10, 196], "widget": {"name": "width_input"}, "link": null}, {"name": "height_input", "type": "INT", "shape": 7, "pos": [10, 220], "widget": {"name": "height_input"}, "link": null}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [418, 641], "slot_index": 0}, {"name": "width", "localized_name": "width", "type": "INT", "links": [524], "slot_index": 1}, {"name": "height", "localized_name": "height", "type": "INT", "links": [525], "slot_index": 2}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 512, "lanc<PERSON>s", false, 32, 0, 0, "disabled"]}, {"id": 328, "type": "LoadImage", "pos": [-70.7983627319336, 1801.9425048828125], "size": [315, 314], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [666], "slot_index": 0}, {"name": "MASK", "localized_name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["shrek2.jpg", "image"]}, {"id": 347, "type": "Note", "pos": [-68.8574447631836, 1695.14990234375], "size": [305.2720947265625, 88], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["First keyframe. The keyframe image size should match the aspect ratio of the generated video."], "color": "#432", "bgcolor": "#653"}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [-285.1499328613281, 843.551025390625], "size": [210, 98], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [607], "slot_index": 0}, {"name": "CLIP", "localized_name": "CLIP", "type": "CLIP", "links": null}, {"name": "VAE", "localized_name": "VAE", "type": "VAE", "links": [721], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ltxv-2b-0.9.6-dev-04-25.safetensors"]}, {"id": 329, "type": "LoadImage", "pos": [1280, 1660], "size": [315, 314], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [668], "slot_index": 0}, {"name": "MASK", "localized_name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["shrek3.jpg", "image"]}, {"id": 349, "type": "Note", "pos": [1300, 2030], "size": [305.2720947265625, 88], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Last keyframe. The keyframe image size should match the aspect ratio of the generated video."], "color": "#432", "bgcolor": "#653"}, {"id": 352, "type": "LTXVCropGuides", "pos": [2006.0809326171875, 815.************], "size": [216.59999084472656, 66], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 741}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 742}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 739}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": null}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": null}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [740], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVCropGuides"}, "widgets_values": []}, {"id": 338, "type": "LTXVPreprocess", "pos": [1290, 1550], "size": [315, 58], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 668}], "outputs": [{"name": "output_image", "localized_name": "output_image", "type": "IMAGE", "links": [733], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVPreprocess"}, "widgets_values": [35]}, {"id": 350, "type": "LTXVAddGuide", "pos": [936.05810546875, 1712.8211669921875], "size": [315, 162], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 722}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 723}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 724}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 725}, {"name": "image", "localized_name": "image", "type": "IMAGE", "link": 726}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [729], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [730], "slot_index": 1}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [743], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVAddGuide"}, "widgets_values": [0, 1]}, {"id": 309, "type": "LTXVConditioning", "pos": [1350, 1140], "size": [210, 78], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 734}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 735}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [639, 741], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [640, 742], "slot_index": 1}], "properties": {"Node name for S&R": "LTXVConditioning"}, "widgets_values": [24]}, {"id": 351, "type": "LTXVAddGuide", "pos": [1290, 1330], "size": [315, 162], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 729}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 730}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 731}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 743}, {"name": "image", "localized_name": "image", "type": "IMAGE", "link": 733}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [734], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [735], "slot_index": 1}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [737, 738], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVAddGuide"}, "widgets_values": [168, 1]}, {"id": 282, "type": "VHS_LoadVideo", "pos": [-289.16888427734375, 1216.662109375], "size": [252.056640625, 406.5318603515625], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "meta_batch", "localized_name": "meta_batch", "type": "VHS_BatchManager", "shape": 7, "link": null}, {"name": "vae", "localized_name": "vae", "type": "VAE", "shape": 7, "link": null}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [541], "slot_index": 0}, {"name": "frame_count", "localized_name": "frame_count", "type": "INT", "links": null}, {"name": "audio", "localized_name": "audio", "type": "AUDIO", "links": null}, {"name": "video_info", "localized_name": "video_info", "type": "VHS_VIDEOINFO", "links": null}], "properties": {"Node name for S&R": "VHS_LoadVideo"}, "widgets_values": {"video": "shot.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 512, "custom_height": 512, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1, "choose video to upload": "image", "videopreview": {"hidden": false, "paused": false, "params": {"force_rate": 0, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1, "filename": "shot.mp4", "type": "input", "format": "video/mp4"}, "muted": false}}}, {"id": 312, "type": "GetNode", "pos": [1650, 750], "size": [210, 58], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [615], "slot_index": 0}], "title": "Get_MODEL", "properties": {}, "widgets_values": ["MODEL"]}, {"id": 209, "type": "GetNode", "pos": [2019.1016845703125, 760.4979858398438], "size": [210, 58], "flags": {"collapsed": true}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [425], "slot_index": 0}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 8, "type": "VAEDecode", "pos": [2256.945556640625, 819.02978515625], "size": [210, 46], "flags": {"collapsed": false}, "order": 39, "mode": 0, "inputs": [{"name": "samples", "localized_name": "samples", "type": "LATENT", "link": 740}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 425}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [744], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 79, "type": "VHS_VideoCombine", "pos": [2041.8179931640625, 944.505126953125], "size": [750, 810.6666870117188], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "images", "localized_name": "images", "type": "IMAGE", "shape": 7, "link": 744}, {"name": "audio", "localized_name": "audio", "type": "AUDIO", "shape": 7, "link": null}, {"name": "meta_batch", "localized_name": "meta_batch", "type": "VHS_BatchManager", "shape": 7, "link": null}, {"name": "vae", "localized_name": "vae", "type": "VAE", "shape": 7, "link": null}], "outputs": [{"name": "Filenames", "localized_name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltx_rf_edit", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 6, "save_metadata": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltx_rf_edit_00002.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24}, "muted": false}}}], "links": [[193, 7, 0, 69, 1, "CONDITIONING"], [258, 84, 0, 119, 0, "*"], [259, 120, 0, 6, 0, "CLIP"], [260, 120, 0, 7, 0, "CLIP"], [370, 6, 0, 69, 0, "CONDITIONING"], [418, 194, 0, 133, 0, "IMAGE"], [425, 209, 0, 8, 1, "VAE"], [486, 242, 0, 244, 0, "IMAGE"], [489, 242, 0, 244, 1, "IMAGE"], [524, 194, 1, 266, 0, "*"], [525, 194, 2, 267, 0, "*"], [541, 282, 0, 194, 0, "IMAGE"], [542, 38, 0, 118, 0, "CLIP"], [581, 308, 0, 305, 0, "NOISE"], [583, 71, 0, 305, 3, "SIGMAS"], [587, 120, 0, 310, 0, "CLIP"], [588, 120, 0, 311, 0, "CLIP"], [594, 314, 0, 315, 1, "VAE"], [607, 44, 0, 84, 0, "MODEL"], [608, 321, 0, 305, 2, "SAMPLER"], [609, 69, 0, 322, 1, "CONDITIONING"], [610, 69, 1, 322, 2, "CONDITIONING"], [613, 316, 0, 322, 0, "MODEL"], [614, 322, 0, 305, 1, "GUIDER"], [615, 312, 0, 323, 0, "MODEL"], [616, 323, 0, 316, 0, "MODEL"], [639, 309, 0, 322, 3, "CONDITIONING"], [640, 309, 1, 322, 4, "CONDITIONING"], [641, 194, 0, 332, 0, "IMAGE"], [642, 332, 0, 315, 0, "IMAGE"], [666, 328, 0, 337, 0, "IMAGE"], [668, 329, 0, 338, 0, "IMAGE"], [721, 44, 2, 90, 0, "VAE"], [722, 310, 0, 350, 0, "CONDITIONING"], [723, 311, 0, 350, 1, "CONDITIONING"], [724, 314, 0, 350, 2, "VAE"], [725, 315, 0, 350, 3, "LATENT"], [726, 337, 0, 350, 4, "IMAGE"], [729, 350, 0, 351, 0, "CONDITIONING"], [730, 350, 1, 351, 1, "CONDITIONING"], [731, 314, 0, 351, 2, "VAE"], [733, 338, 0, 351, 4, "IMAGE"], [734, 351, 0, 309, 0, "CONDITIONING"], [735, 351, 1, 309, 1, "CONDITIONING"], [737, 351, 2, 71, 0, "LATENT"], [738, 351, 2, 305, 4, "LATENT"], [739, 305, 0, 352, 2, "LATENT"], [740, 352, 2, 8, 0, "LATENT"], [741, 309, 0, 352, 0, "CONDITIONING"], [742, 309, 1, 352, 1, "CONDITIONING"], [743, 350, 2, 351, 3, "LATENT"], [744, 8, 0, 79, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Setup", "bounding": [-299.16888427734375, 767.5498657226562, 520.8411865234375, 797.7100219726562], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [267, 266, 133, 84, 119, 38, 118, 90, 194, 44, 282]}, {"id": 2, "title": "Sampling", "bounding": [262.622314453125, 682.203125, 1709.0252685546875, 1258.6478271484375], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [120, 7, 316, 305, 308, 323, 311, 6, 332, 322, 337, 69, 314, 315, 71, 321, 310, 329, 338, 350, 309, 351, 312]}, {"id": 7, "title": "Output", "bounding": [1997.9552001953125, 686.3687133789062, 831.3340454101562, 1133.1993408203125], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [352, 209, 8, 79]}], "config": {}, "extra": {"ds": {"scale": 0.6544837670562416, "offset": [330.5733898652322, -671.9515388667731]}, "node_versions": {"comfy-core": "0.3.20"}, "prompt": {"6": {"inputs": {"text": "a 30 year old Israeli man speaking and moving around. The room is dim. The camera is static. The footage semms to come from real world footage. ", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "shaky, glitchy, low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["352", 2], "vae": ["44", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "38": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "44": {"inputs": {"ckpt_name": "ltxv-2b-0.9.6-dev-04-25.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "69": {"inputs": {"frame_rate": 24, "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "71": {"inputs": {"steps": 30, "max_shift": 2.05, "base_shift": 0.95, "stretch": true, "terminal": 0.1, "latent": ["351", 2]}, "class_type": "LTXVScheduler", "_meta": {"title": "LTXVScheduler"}}, "79": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltx_rf_edit", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 6, "save_metadata": false, "pingpong": false, "save_output": false, "images": ["8", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "84": {"inputs": {"model": ["44", 0]}, "class_type": "ModifyLTXModel", "_meta": {"title": "Modify LTX Model"}}, "194": {"inputs": {"width": 768, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 32, "crop": "disabled", "image": ["282", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "282": {"inputs": {"video": "shot.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 512, "custom_height": 512, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1}, "class_type": "VHS_LoadVideo", "_meta": {"title": "Load Video (Upload) 🎥🅥🅗🅢"}}, "305": {"inputs": {"noise": ["308", 0], "guider": ["322", 0], "sampler": ["321", 0], "sigmas": ["71", 0], "latent_image": ["351", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "308": {"inputs": {}, "class_type": "<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON>"}}, "309": {"inputs": {"frame_rate": 24, "positive": ["351", 0], "negative": ["351", 1]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "310": {"inputs": {"text": "A character that looks like <PERSON><PERSON><PERSON> from the movie is in a fantasy style room. He standing and speaks. The camera is static. The lighting is natural. The footage seems to come from real world footage.", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "311": {"inputs": {"text": "shaky, glitchy, low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "315": {"inputs": {"pixels": ["332", 0], "vae": ["44", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "316": {"inputs": {"model": ["323", 0]}, "class_type": "LTXReverseModelSamplingPred", "_meta": {"title": "LTX Reverse Model Pred"}}, "321": {"inputs": {"skip_steps": 3, "refine_steps": 6, "seed": 0}, "class_type": "LTXFlowEditSampler", "_meta": {"title": "LTX Flow Edit Sampler"}}, "322": {"inputs": {"source_cfg": 2, "target_cfg": 6.5, "model": ["316", 0], "source_pos": ["69", 0], "source_neg": ["69", 1], "target_pos": ["309", 0], "target_neg": ["309", 1]}, "class_type": "LTXFlowEditCFGGuider", "_meta": {"title": "LTX Flow Edit CFG Guider"}}, "323": {"inputs": {"scale": 1, "rescale": 0.75, "cfg": 5, "model": ["84", 0]}, "class_type": "LTXPerturbedAttention", "_meta": {"title": "LTX Apply Perturbed Attention"}}, "328": {"inputs": {"image": "shrek2.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "329": {"inputs": {"image": "shrek3.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "332": {"inputs": {"image": ["194", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "337": {"inputs": {"img_compression": 35, "image": ["328", 0]}, "class_type": "LTXVPreprocess", "_meta": {"title": "LTXVPreprocess"}}, "338": {"inputs": {"img_compression": 35, "image": ["329", 0]}, "class_type": "LTXVPreprocess", "_meta": {"title": "LTXVPreprocess"}}, "350": {"inputs": {"frame_idx": 0, "strength": 1, "positive": ["310", 0], "negative": ["311", 0], "vae": ["44", 2], "latent": ["315", 0], "image": ["337", 0]}, "class_type": "LTXVAddGuide", "_meta": {"title": "LTXVAddGuide"}}, "351": {"inputs": {"frame_idx": 168, "strength": 1, "positive": ["350", 0], "negative": ["350", 1], "vae": ["44", 2], "latent": ["350", 2], "image": ["338", 0]}, "class_type": "LTXVAddGuide", "_meta": {"title": "LTXVAddGuide"}}, "352": {"inputs": {"positive": ["309", 0], "negative": ["309", 1], "latent": ["305", 0]}, "class_type": "LTXVCropGuides", "_meta": {"title": "LTXVCropGuides"}}}, "comfy_fork_version": "HEAD@3d2b9d62"}, "version": 0.4}