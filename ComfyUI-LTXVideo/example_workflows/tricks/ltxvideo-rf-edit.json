{"last_node_id": 318, "last_link_id": 657, "nodes": [{"id": 71, "type": "LTXVScheduler", "pos": [2701.582763671875, 1362.**********], "size": [210, 154], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "latent", "localized_name": "latent", "type": "LATENT", "shape": 7, "link": 657}], "outputs": [{"name": "SIGMAS", "localized_name": "SIGMAS", "type": "SIGMAS", "links": [182], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVScheduler"}, "widgets_values": [30, 2.05, 0.95, true, 0.1]}, {"id": 127, "type": "GetNode", "pos": [268, 1114], "size": [210, 58], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [263], "slot_index": 0}], "title": "Get_CLIP", "properties": {}, "widgets_values": ["CLIP"], "color": "#432", "bgcolor": "#653"}, {"id": 129, "type": "LTXForwardModelSamplingPred", "pos": [795.*************, 1326.************], "size": [184.*************, 26], "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 339}], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [271], "slot_index": 0}], "properties": {"Node name for S&R": "LTXForwardModelSamplingPred"}, "widgets_values": []}, {"id": 143, "type": "FlipSigmas", "pos": [822.*************, 1415.************], "size": [140, 26], "flags": {"collapsed": true}, "order": 45, "mode": 0, "inputs": [{"name": "sigmas", "localized_name": "sigmas", "type": "SIGMAS", "link": 287}], "outputs": [{"name": "SIGMAS", "localized_name": "SIGMAS", "type": "SIGMAS", "links": [288], "slot_index": 0}], "properties": {"Node name for S&R": "FlipSigmas"}, "widgets_values": []}, {"id": 169, "type": "LTXAttentioOverride", "pos": [1740.************, 851.0711669921875], "size": [210, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LTX_BLOCKS", "localized_name": "LTX_BLOCKS", "type": "LTX_BLOCKS", "links": [341], "slot_index": 0}], "properties": {"Node name for S&R": "LTXAttentioOverride"}, "widgets_values": ["20, 21, 22, 23"]}, {"id": 285, "type": "GetNode", "pos": [1204.9986572265625, 1528.4267578125], "size": [210, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [557], "slot_index": 0}], "title": "Get_WIDTH", "properties": {}, "widgets_values": ["WIDTH"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 286, "type": "GetNode", "pos": [1218.9986572265625, 1587.4267578125], "size": [210, 58], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [558], "slot_index": 0}], "title": "Get_HEIGHT", "properties": {}, "widgets_values": ["HEIGHT"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 209, "type": "GetNode", "pos": [3029, 921], "size": [210, 58], "flags": {"collapsed": true}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [425], "slot_index": 0}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 120, "type": "GetNode", "pos": [2102, 939], "size": [210, 58], "flags": {"collapsed": true}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [259, 260], "slot_index": 0}], "title": "Get_CLIP", "properties": {}, "widgets_values": ["CLIP"], "color": "#432", "bgcolor": "#653"}, {"id": 162, "type": "GetNode", "pos": [823, 1366], "size": [210, 58], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [339], "slot_index": 0}], "title": "Get_MODEL", "properties": {}, "widgets_values": ["MODEL"], "color": "#223", "bgcolor": "#335"}, {"id": 267, "type": "SetNode", "pos": [6.682981967926025, 1478.5572509765625], "size": [210, 58], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 525}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_HEIGHT", "properties": {"previousName": "HEIGHT"}, "widgets_values": ["HEIGHT"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 266, "type": "SetNode", "pos": [6.682981967926025, 1428.5572509765625], "size": [210, 58], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 524}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_WIDTH", "properties": {"previousName": "WIDTH"}, "widgets_values": ["WIDTH"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 133, "type": "SetNode", "pos": [-3.3170180320739746, 1378.5572509765625], "size": [210, 58], "flags": {"collapsed": true}, "order": 37, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 418}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [274], "slot_index": 0}], "title": "Set_INPUT_VID", "properties": {"previousName": "INPUT_VID"}, "widgets_values": ["INPUT_VID"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 141, "type": "GetNode", "pos": [2195, 1499], "size": [210, 58], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [369], "slot_index": 0}], "title": "Get_INPUT_LATENTS", "properties": {}, "widgets_values": ["INPUT_LATENTS"], "color": "#323", "bgcolor": "#535"}, {"id": 297, "type": "Note", "pos": [2183.670654296875, 642.7354125976562], "size": [341.50701904296875, 176.46348571777344], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Prompting\n\nPrompts should be very long and descriptive (e.g. a paragraph).\n\nThey should describe the desired output image and motion."], "color": "#432", "bgcolor": "#653"}, {"id": 147, "type": "LTXRFReverseODESampler", "pos": [2449.582763671875, 1371.**********], "size": [218.**************, 194], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 385}, {"name": "latent_image", "localized_name": "latent_image", "type": "LATENT", "link": 369}, {"name": "attn_inj", "localized_name": "attn_inj", "type": "ATTN_INJ", "shape": 7, "link": 312}], "outputs": [{"name": "SAMPLER", "localized_name": "SAMPLER", "type": "SAMPLER", "links": [305], "slot_index": 0}], "properties": {"Node name for S&R": "LTXRFReverseODESampler"}, "widgets_values": [0, 0, 0, "linear_decrease", "first"]}, {"id": 299, "type": "Note", "pos": [522, 1690], "size": [347.0309753417969, 333.83843994140625], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Forward Sampler\n\n* Gamma controls the amount of noise that's injected into unsampling.\n\nThis can help if you're trying to make a very large shift away from the input video.\n\nIf used I suggest end_step = total_steps/2 and gamma = 0.5, with linear_decrease\n\nThis can help decrease the exact adherence to the input video.\n\n\n* Order\n\nOrder is the amount of inner steps to do which could increase the information gathered from the input video.\n\nCurrently gamma > 0 and Second Order do not work well together."], "color": "#432", "bgcolor": "#653"}, {"id": 298, "type": "Note", "pos": [-700, 1221], "size": [312.*************, 220.*************], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Input Video\n\nInput video should be ( 8 * N ) + 1 frames long, e.g.\n\n17\n25\n33\n41\n49\n...\nso on"], "color": "#432", "bgcolor": "#653"}, {"id": 146, "type": "LTXRFForwardODESampler", "pos": [545.*************, 1418.************], "size": [218.**************, 202], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "attn_bank", "localized_name": "attn_bank", "type": "ATTN_BANK", "shape": 7, "link": 374}], "outputs": [{"name": "SAMPLER", "localized_name": "SAMPLER", "type": "SAMPLER", "links": [296], "slot_index": 0}], "properties": {"Node name for S&R": "LTXRFForwardODESampler"}, "widgets_values": [0.5, 0, 15, "linear_decrease", 0, "fixed", "first"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [2129, 1211], "size": [285.*************, 114.**************], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 260}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [193], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["shaky, glitchy, low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"], "color": "#322", "bgcolor": "#533"}, {"id": 304, "type": "Note", "pos": [2425.3134765625, 1648.29052734375], "size": [273.5052185058594, 325.4657287597656], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Reverse Sampling\n\n* eta \n\nControls how strong the input video latents influence the sampling\n\nThis is an alternative to attention injection, but can also be used in conjunction\n\nIf the Forward Sampler has \"First\" order, this can not use \"Second\" order and attention injections since the second order attentions were not unsampled.\n"], "color": "#432", "bgcolor": "#653"}, {"id": 296, "type": "Note", "pos": [1700.58544921875, 250.21739196777344], "size": [314.57293701171875, 269.6781921386719], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Attention Injection\n\nControls how much of the unsampled attention is injected into the sampling. \n\nIncreasing this will increase adherence to the input video.\n\nYou can save as much attention as you like, and then choose how much to inject here.\n\nBlocks refer to the LTX Model's block to save/inject on.\n\nInjecting too much may mean that if you are using an input image, the model has too much style information from the input video and disregards the input image."], "color": "#432", "bgcolor": "#653"}, {"id": 150, "type": "LTXPrepareAttnInjections", "pos": [1732.************, 629.*************], "size": [218.**************, 170], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 645}, {"name": "attn_bank", "localized_name": "attn_bank", "type": "ATTN_BANK", "link": 373}, {"name": "blocks", "localized_name": "blocks", "type": "LTX_BLOCKS", "shape": 7, "link": 341}], "outputs": [{"name": "LATENT", "localized_name": "LATENT", "type": "LATENT", "links": [], "slot_index": 0}, {"name": "ATTN_INJ", "localized_name": "ATTN_INJ", "type": "ATTN_INJ", "links": [312], "slot_index": 1}], "properties": {"Node name for S&R": "LTXPrepareAttnInjections"}, "widgets_values": [false, false, true, 7]}, {"id": 136, "type": "LTXReverseModelSamplingPred", "pos": [2194.********, 1552.**********], "size": [184.*************, 26], "flags": {"collapsed": false}, "order": 27, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 577}], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [385, 386], "slot_index": 0}], "properties": {"Node name for S&R": "LTXReverseModelSamplingPred"}, "widgets_values": []}, {"id": 180, "type": "GetNode", "pos": [1134.*************, 1436.************], "size": [210, 58], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [577], "slot_index": 0}], "title": "Get_MODEL", "properties": {}, "widgets_values": ["MODEL"], "color": "#223", "bgcolor": "#335"}, {"id": 69, "type": "LTXVConditioning", "pos": [2469.203125, 950.6239013671875], "size": [210, 78], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 370}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 193}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [646], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [647], "slot_index": 1}], "properties": {"Node name for S&R": "LTXVConditioning"}, "widgets_values": [24]}, {"id": 151, "type": "LTXAttentionBank", "pos": [225.**************, 799.************], "size": [210, 112], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "ATTN_BANK", "localized_name": "ATTN_BANK", "type": "ATTN_BANK", "links": [373, 374], "slot_index": 0}], "properties": {"Node name for S&R": "LTXAttentionBank"}, "widgets_values": [15, "15,16,17,18,19,20,21,22,23,24,25,26,27"]}, {"id": 79, "type": "VHS_VideoCombine", "pos": [3129.**********, 1250.************], "size": [920.*************, 310], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "images", "localized_name": "images", "type": "IMAGE", "shape": 7, "link": 570}, {"name": "audio", "localized_name": "audio", "type": "AUDIO", "shape": 7, "link": null}, {"name": "meta_batch", "localized_name": "meta_batch", "type": "VHS_BatchManager", "shape": 7, "link": null}, {"name": "vae", "localized_name": "vae", "type": "VAE", "shape": 7, "link": null}], "outputs": [{"name": "Filenames", "localized_name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltx_rf_edit", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 6, "save_metadata": false, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "ltx_rf_edit_00013.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 24}, "muted": false}}}, {"id": 38, "type": "CLIPLoader", "pos": [-319.6058349609375, 998.5630493164062], "size": [210, 98], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "localized_name": "CLIP", "type": "CLIP", "links": [542], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "ltxv", "default"]}, {"id": 84, "type": "ModifyLTXModel", "pos": [-67.00086212158203, 797.658447265625], "size": [140, 26], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 204}], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [258], "slot_index": 0}], "properties": {"Node name for S&R": "ModifyLTXModel"}, "widgets_values": []}, {"id": 119, "type": "SetNode", "pos": [45.011898040771484, 836.6636352539062], "size": [210, 58], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"name": "MODEL", "type": "MODEL", "link": 258}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_MODEL", "properties": {"previousName": "MODEL"}, "widgets_values": ["MODEL"], "color": "#223", "bgcolor": "#335"}, {"id": 118, "type": "SetNode", "pos": [-308.5791015625, 1130.788330078125], "size": [210, 58], "flags": {"collapsed": true}, "order": 29, "mode": 0, "inputs": [{"name": "CLIP", "type": "CLIP", "link": 542}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_CLIP", "properties": {"previousName": "CLIP"}, "widgets_values": ["CLIP"], "color": "#432", "bgcolor": "#653"}, {"id": 90, "type": "SetNode", "pos": [-41.852699279785156, 1057.0926513671875], "size": [210, 58], "flags": {"collapsed": true}, "order": 31, "mode": 0, "inputs": [{"name": "VAE", "type": "VAE", "link": 614}], "outputs": [{"name": "*", "type": "*", "links": [], "slot_index": 0}], "title": "Set_VAE", "properties": {"previousName": "VAE"}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 124, "type": "CLIPTextEncode", "pos": [237.22181701660156, 1042.2420654296875], "size": [285.*************, 88], "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 263}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [630, 631], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 188, "type": "GetNode", "pos": [1145.1175537109375, 1294.59326171875], "size": [210, 58], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [648], "slot_index": 0}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 313, "type": "GetNode", "pos": [454.513427734375, 802.98974609375], "size": [210, 58], "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [632], "slot_index": 0}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 195, "type": "GetNode", "pos": [262.6222839355469, 1252.0526123046875], "size": [210, 58], "flags": {"collapsed": false}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [398], "slot_index": 0}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 308, "type": "LTXVPreprocess", "pos": [1394.874755859375, 1766.055419921875], "size": [315, 58], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 594}], "outputs": [{"name": "output_image", "localized_name": "output_image", "type": "IMAGE", "links": [595], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVPreprocess"}, "widgets_values": [35]}, {"id": 121, "type": "LTXVScheduler", "pos": [789.1510009765625, 1456.632568359375], "size": [210, 154], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "latent", "localized_name": "latent", "type": "LATENT", "shape": 7, "link": 634}], "outputs": [{"name": "SIGMAS", "localized_name": "SIGMAS", "type": "SIGMAS", "links": [287], "slot_index": 0}], "properties": {"Node name for S&R": "LTXVScheduler"}, "widgets_values": [30, 2.05, 0.95, true, 0.1]}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [-319.74578857421875, 798.3598022460938], "size": [237.47021484375, 105.68223571777344], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "localized_name": "MODEL", "type": "MODEL", "links": [204], "slot_index": 0}, {"name": "CLIP", "localized_name": "CLIP", "type": "CLIP", "links": null}, {"name": "VAE", "localized_name": "VAE", "type": "VAE", "links": [614], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ltxv-2b-0.9.6-dev-04-25.safetensors"]}, {"id": 314, "type": "VHS_SelectImages", "pos": [-29.28087043762207, 1718.9381103515625], "size": [315, 106], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 625}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [636], "slot_index": 0}], "properties": {"Node name for S&R": "VHS_SelectImages"}, "widgets_values": {"indexes": "0", "err_if_missing": true, "err_if_empty": true}}, {"id": 194, "type": "ImageResizeKJ", "pos": [-40.16438674926758, 1097.2681884765625], "size": [210, 286], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 541}, {"name": "get_image_size", "localized_name": "get_image_size", "type": "IMAGE", "shape": 7, "link": null}, {"name": "width_input", "type": "INT", "shape": 7, "pos": [10, 196], "widget": {"name": "width_input"}, "link": null}, {"name": "height_input", "type": "INT", "shape": 7, "pos": [10, 220], "widget": {"name": "height_input"}, "link": null}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [418, 625], "slot_index": 0}, {"name": "width", "localized_name": "width", "type": "INT", "links": [524], "slot_index": 1}, {"name": "height", "localized_name": "height", "type": "INT", "links": [525], "slot_index": 2}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [1280, 720, "lanc<PERSON>s", false, 32, 0, 0, "center"]}, {"id": 8, "type": "VAEDecode", "pos": [3349.45751953125, 999.4630737304688], "size": [210, 46], "flags": {"collapsed": false}, "order": 54, "mode": 0, "inputs": [{"name": "samples", "localized_name": "samples", "type": "LATENT", "link": 629}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 425}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [570], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 6, "type": "CLIPTextEncode", "pos": [2096.78759765625, 985.394287109375], "size": [342.4654541015625, 184.09689331054688], "flags": {"collapsed": false}, "order": 24, "mode": 0, "inputs": [{"name": "clip", "localized_name": "clip", "type": "CLIP", "link": 259}], "outputs": [{"name": "CONDITIONING", "localized_name": "CONDITIONING", "type": "CONDITIONING", "links": [370], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a young woman holding a colorful ice cream cone. She is wearing a purple collared shirt and has blonde hair styled in loose waves. The ice cream is in the shape of a cloud with a rainbow-colored swirl on top. The woman is looking directly at the camera with a serious expression on her face. In the background, there is a neon sign that reads \"NODD\" in bold, colorful letters. The overall mood of the image is playful and vibrant."], "color": "#232", "bgcolor": "#353"}, {"id": 137, "type": "SetNode", "pos": [535.8656005859375, 1251.5623779296875], "size": [210, 58], "flags": {"collapsed": false}, "order": 43, "mode": 0, "inputs": [{"name": "LATENT", "type": "LATENT", "link": 635}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [633, 634], "slot_index": 0}], "title": "Set_INPUT_LATENTS", "properties": {"previousName": "INPUT_LATENTS"}, "widgets_values": ["INPUT_LATENTS"], "color": "#323", "bgcolor": "#535"}, {"id": 134, "type": "VAEEncode", "pos": [502.8349304199219, 1105.9522705078125], "size": [210, 46], "flags": {"collapsed": false}, "order": 41, "mode": 0, "inputs": [{"name": "pixels", "localized_name": "pixels", "type": "IMAGE", "link": 274}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 398}], "outputs": [{"name": "LATENT", "localized_name": "LATENT", "type": "LATENT", "links": [640], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 316, "type": "LTXVAddGuide", "pos": [683.4859619140625, 805.5009155273438], "size": [315, 162], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 630}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 631}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 632}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 640}, {"name": "image", "localized_name": "image", "type": "IMAGE", "link": 636}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [638, 641], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [639, 642], "slot_index": 1}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [635], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVAddGuide"}, "widgets_values": [0, 1]}, {"id": 122, "type": "SamplerCustom", "pos": [757.4413452148438, 1035.1241455078125], "size": [236.*************, 230], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 271}, {"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 638}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 639}, {"name": "sampler", "localized_name": "sampler", "type": "SAMPLER", "link": 296}, {"name": "sigmas", "localized_name": "sigmas", "type": "SIGMAS", "link": 288}, {"name": "latent_image", "localized_name": "latent_image", "type": "LATENT", "link": 633}], "outputs": [{"name": "output", "localized_name": "output", "type": "LATENT", "links": [643], "slot_index": 0}, {"name": "denoised_output", "localized_name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "SamplerCustom"}, "widgets_values": [false, 2, "fixed", 1]}, {"id": 295, "type": "Note", "pos": [1120.08349609375, 834.2901000976562], "size": [312.*************, 220.*************], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Image Injection\n\nReplaces or inserts the image latent into the video latent.\n\nWhen insert is Off, the image latent replaces the video latent.\n\nWhen insert is On, the image latent is inserted into the video latents, extending the length of the video.\n\nNote that latents are technically 8 frames long, but your image latent is only 1 frame, so this could cause issues if you replace/insert in the middle of the video."], "color": "#432", "bgcolor": "#653"}, {"id": 284, "type": "ImageResizeKJ", "pos": [1374.405517578125, 1389.30078125], "size": [315, 326], "flags": {"collapsed": false}, "order": 36, "mode": 0, "inputs": [{"name": "image", "localized_name": "image", "type": "IMAGE", "link": 595}, {"name": "get_image_size", "localized_name": "get_image_size", "type": "IMAGE", "shape": 7, "link": null}, {"name": "width_input", "type": "INT", "shape": 7, "pos": [10, 196], "widget": {"name": "width_input"}, "link": null}, {"name": "height_input", "type": "INT", "shape": 7, "pos": [10, 220], "widget": {"name": "height_input"}, "link": null}, {"name": "width", "type": "INT", "pos": [10, 76], "widget": {"name": "width"}, "link": 557}, {"name": "height", "type": "INT", "pos": [10, 100], "widget": {"name": "height"}, "link": 558}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [649], "slot_index": 0}, {"name": "width", "localized_name": "width", "type": "INT", "links": null}, {"name": "height", "localized_name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [512, 512, "nearest-exact", false, 2, 0, 0, "disabled"]}, {"id": 315, "type": "LTXVCropGuides", "pos": [3034.************, 994.13134765625], "size": [216.59999084472656, 66], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 653}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 654}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 626}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": null}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": null}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [629], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVCropGuides"}, "widgets_values": []}, {"id": 72, "type": "SamplerCustom", "pos": [2690.************, 1085.57861328125], "size": [236.*************, 230], "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "model", "localized_name": "model", "type": "MODEL", "link": 386}, {"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 651}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 652}, {"name": "sampler", "localized_name": "sampler", "type": "SAMPLER", "link": 305}, {"name": "sigmas", "localized_name": "sigmas", "type": "SIGMAS", "link": 182}, {"name": "latent_image", "localized_name": "latent_image", "type": "LATENT", "link": 656}], "outputs": [{"name": "output", "localized_name": "output", "type": "LATENT", "links": [626], "slot_index": 0}, {"name": "denoised_output", "localized_name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "SamplerCustom"}, "widgets_values": [false, 0, "fixed", 4]}, {"id": 317, "type": "LTXVCropGuides", "pos": [1170.37353515625, 707.1304931640625], "size": [216.59999084472656, 66], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 641}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 642}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 643}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": null}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": null}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [645, 655], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVCropGuides"}, "widgets_values": []}, {"id": 318, "type": "LTXVAddGuide", "pos": [1434.0797119140625, 1145.9578857421875], "size": [315, 162], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "link": 646}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "link": 647}, {"name": "vae", "localized_name": "vae", "type": "VAE", "link": 648}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "link": 655}, {"name": "image", "localized_name": "image", "type": "IMAGE", "link": 649}], "outputs": [{"name": "positive", "localized_name": "positive", "type": "CONDITIONING", "links": [651, 653], "slot_index": 0}, {"name": "negative", "localized_name": "negative", "type": "CONDITIONING", "links": [652, 654], "slot_index": 1}, {"name": "latent", "localized_name": "latent", "type": "LATENT", "links": [656, 657], "slot_index": 2}], "properties": {"Node name for S&R": "LTXVAddGuide"}, "widgets_values": [0, 1]}, {"id": 283, "type": "LoadImage", "pos": [958.5836181640625, 1708.************], "size": [315, 314], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [594], "slot_index": 0}, {"name": "MASK", "localized_name": "MASK", "type": "MASK", "links": null, "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["ref.png", "image"]}, {"id": 282, "type": "VHS_LoadVideo", "pos": [-327.5198059082031, 1176.494140625], "size": [252.056640625, 406.5318603515625], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "meta_batch", "localized_name": "meta_batch", "type": "VHS_BatchManager", "shape": 7, "link": null}, {"name": "vae", "localized_name": "vae", "type": "VAE", "shape": 7, "link": null}], "outputs": [{"name": "IMAGE", "localized_name": "IMAGE", "type": "IMAGE", "links": [541], "slot_index": 0}, {"name": "frame_count", "localized_name": "frame_count", "type": "INT", "links": null, "slot_index": 1}, {"name": "audio", "localized_name": "audio", "type": "AUDIO", "links": null}, {"name": "video_info", "localized_name": "video_info", "type": "VHS_VIDEOINFO", "links": null}], "properties": {"Node name for S&R": "VHS_LoadVideo"}, "widgets_values": {"video": "shot2.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 512, "custom_height": 512, "frame_load_cap": 65, "skip_first_frames": 0, "select_every_nth": 1, "choose video to upload": "image", "videopreview": {"hidden": false, "paused": false, "params": {"force_rate": 0, "frame_load_cap": 65, "skip_first_frames": 0, "select_every_nth": 1, "filename": "shot2.mp4", "type": "input", "format": "video/mp4"}, "muted": false}}}], "links": [[182, 71, 0, 72, 4, "SIGMAS"], [193, 7, 0, 69, 1, "CONDITIONING"], [204, 44, 0, 84, 0, "MODEL"], [258, 84, 0, 119, 0, "*"], [259, 120, 0, 6, 0, "CLIP"], [260, 120, 0, 7, 0, "CLIP"], [263, 127, 0, 124, 0, "CLIP"], [271, 129, 0, 122, 0, "MODEL"], [274, 133, 0, 134, 0, "IMAGE"], [287, 121, 0, 143, 0, "SIGMAS"], [288, 143, 0, 122, 4, "SIGMAS"], [296, 146, 0, 122, 3, "SAMPLER"], [305, 147, 0, 72, 3, "SAMPLER"], [312, 150, 1, 147, 2, "ATTN_INJ"], [339, 162, 0, 129, 0, "MODEL"], [341, 169, 0, 150, 2, "LTX_BLOCKS"], [369, 141, 0, 147, 1, "LATENT"], [370, 6, 0, 69, 0, "CONDITIONING"], [373, 151, 0, 150, 1, "ATTN_BANK"], [374, 151, 0, 146, 0, "ATTN_BANK"], [385, 136, 0, 147, 0, "MODEL"], [386, 136, 0, 72, 0, "MODEL"], [398, 195, 0, 134, 1, "VAE"], [418, 194, 0, 133, 0, "IMAGE"], [425, 209, 0, 8, 1, "VAE"], [486, 242, 0, 244, 0, "IMAGE"], [489, 242, 0, 244, 1, "IMAGE"], [524, 194, 1, 266, 0, "*"], [525, 194, 2, 267, 0, "*"], [541, 282, 0, 194, 0, "IMAGE"], [542, 38, 0, 118, 0, "CLIP"], [557, 285, 0, 284, 4, "INT"], [558, 286, 0, 284, 5, "INT"], [570, 8, 0, 79, 0, "IMAGE"], [577, 180, 0, 136, 0, "MODEL"], [594, 283, 0, 308, 0, "IMAGE"], [595, 308, 0, 284, 0, "IMAGE"], [614, 44, 2, 90, 0, "VAE"], [625, 194, 0, 314, 0, "IMAGE"], [626, 72, 0, 315, 2, "LATENT"], [629, 315, 2, 8, 0, "LATENT"], [630, 124, 0, 316, 0, "CONDITIONING"], [631, 124, 0, 316, 1, "CONDITIONING"], [632, 313, 0, 316, 2, "VAE"], [633, 137, 0, 122, 5, "LATENT"], [634, 137, 0, 121, 0, "LATENT"], [635, 316, 2, 137, 0, "LATENT"], [636, 314, 0, 316, 4, "IMAGE"], [638, 316, 0, 122, 1, "CONDITIONING"], [639, 316, 1, 122, 2, "CONDITIONING"], [640, 134, 0, 316, 3, "LATENT"], [641, 316, 0, 317, 0, "CONDITIONING"], [642, 316, 1, 317, 1, "CONDITIONING"], [643, 122, 0, 317, 2, "LATENT"], [645, 317, 2, 150, 0, "LATENT"], [646, 69, 0, 318, 0, "CONDITIONING"], [647, 69, 1, 318, 1, "CONDITIONING"], [648, 188, 0, 318, 2, "VAE"], [649, 284, 0, 318, 4, "IMAGE"], [651, 318, 0, 72, 1, "CONDITIONING"], [652, 318, 1, 72, 2, "CONDITIONING"], [653, 318, 0, 315, 0, "CONDITIONING"], [654, 318, 1, 315, 1, "CONDITIONING"], [655, 317, 2, 318, 3, "LATENT"], [656, 318, 2, 72, 5, "LATENT"], [657, 318, 2, 71, 0, "LATENT"]], "groups": [{"id": 1, "title": "Setup", "bounding": [-337.5198059082031, 727.3812255859375, 520.8411865234375, 797.7100219726562], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [267, 266, 133, 38, 84, 119, 118, 90, 44, 194, 282]}, {"id": 2, "title": "Unsample", "bounding": [215.**************, 725.6488037109375, 796.800048828125, 904.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [127, 129, 143, 162, 146, 151, 124, 313, 195, 121, 137, 134, 316, 122]}, {"id": 3, "title": "<PERSON><PERSON>", "bounding": [2081.********, 864.913818359375, 855.799560546875, 710.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [71, 120, 141, 147, 7, 136, 69, 6, 72]}, {"id": 7, "title": "Output", "bounding": [3007.854248046875, 846.8707275390625, 1301.0289306640625, 1577.6492919921875], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [209, 79, 8, 315]}, {"id": 8, "title": "Image Injection", "bounding": [1119.2138671875, 1075.7376708984375, 638.3729248046875, 1087.2047119140625], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [285, 286, 180, 188, 308, 284, 318]}, {"id": 14, "title": "Attention Injection", "bounding": [1630.46728515625, 560.5890502929688, 442.3812561035156, 406.841064453125], "color": "#3f789e", "font_size": 24, "flags": {}, "nodes": [169, 150]}], "config": {}, "extra": {"ds": {"scale": 0.4017453294694398, "offset": [1159.7126679724122, -202.68124882117442]}, "node_versions": {"comfy-core": "0.3.20"}, "prompt": {"6": {"inputs": {"text": "a young woman holding a colorful ice cream cone. She is wearing a purple collared shirt and has blonde hair styled in loose waves. The ice cream is in the shape of a cloud with a rainbow-colored swirl on top. The woman is looking directly at the camera with a serious expression on her face. In the background, there is a neon sign that reads \"NODD\" in bold, colorful letters. The overall mood of the image is playful and vibrant.", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "shaky, glitchy, low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["315", 2], "vae": ["44", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "38": {"inputs": {"clip_name": "t5xxl_fp16.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "44": {"inputs": {"ckpt_name": "ltxv-2b-0.9.6-dev-04-25.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "69": {"inputs": {"frame_rate": 24, "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXVConditioning"}}, "71": {"inputs": {"steps": 30, "max_shift": 2.05, "base_shift": 0.95, "stretch": true, "terminal": 0.1, "latent": ["318", 2]}, "class_type": "LTXVScheduler", "_meta": {"title": "LTXVScheduler"}}, "72": {"inputs": {"add_noise": false, "noise_seed": 0, "cfg": 4, "model": ["136", 0], "positive": ["318", 0], "negative": ["318", 1], "sampler": ["147", 0], "sigmas": ["71", 0], "latent_image": ["318", 2]}, "class_type": "SamplerCustom", "_meta": {"title": "SamplerCustom"}}, "79": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltx_rf_edit", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 6, "save_metadata": false, "pingpong": false, "save_output": true, "images": ["8", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "84": {"inputs": {"model": ["44", 0]}, "class_type": "ModifyLTXModel", "_meta": {"title": "Modify LTX Model"}}, "121": {"inputs": {"steps": 30, "max_shift": 2.05, "base_shift": 0.95, "stretch": true, "terminal": 0.1, "latent": ["316", 2]}, "class_type": "LTXVScheduler", "_meta": {"title": "LTXVScheduler"}}, "122": {"inputs": {"add_noise": false, "noise_seed": 2, "cfg": 1, "model": ["129", 0], "positive": ["316", 0], "negative": ["316", 1], "sampler": ["146", 0], "sigmas": ["143", 0], "latent_image": ["316", 2]}, "class_type": "SamplerCustom", "_meta": {"title": "SamplerCustom"}}, "124": {"inputs": {"text": "", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "129": {"inputs": {"model": ["84", 0]}, "class_type": "LTXForwardModelSamplingPred", "_meta": {"title": "LTX Forward Model Pred"}}, "134": {"inputs": {"pixels": ["194", 0], "vae": ["44", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "136": {"inputs": {"model": ["84", 0]}, "class_type": "LTXReverseModelSamplingPred", "_meta": {"title": "LTX Reverse Model Pred"}}, "143": {"inputs": {"sigmas": ["121", 0]}, "class_type": "FlipSigmas", "_meta": {"title": "FlipSigmas"}}, "146": {"inputs": {"gamma": 0.5, "start_step": 0, "end_step": 15, "gamma_trend": "linear_decrease", "seed": 0, "order": "first", "attn_bank": ["151", 0]}, "class_type": "LTXRFForwardODESampler", "_meta": {"title": "LTX Rf-Inv Forward Sampler"}}, "147": {"inputs": {"eta": 0, "start_step": 0, "end_step": 0, "eta_trend": "linear_decrease", "order": "first", "model": ["136", 0], "latent_image": ["316", 2], "attn_inj": ["150", 1]}, "class_type": "LTXRFReverseODESampler", "_meta": {"title": "LTX Rf-Inv Reverse Sampler"}}, "150": {"inputs": {"query": false, "key": false, "value": true, "inject_steps": 7, "latent": ["317", 2], "attn_bank": ["151", 0], "blocks": ["169", 0]}, "class_type": "LTXPrepareAttnInjections", "_meta": {"title": "LTX Prepare Attn Injection"}}, "151": {"inputs": {"save_steps": 15, "blocks": "15,16,17,18,19,20,21,22,23,24,25,26,27"}, "class_type": "LTXAttentionBank", "_meta": {"title": "LTX Attention Bank"}}, "169": {"inputs": {"blocks": "20, 21, 22, 23"}, "class_type": "LTXAttentioOverride", "_meta": {"title": "LTX Attn Block Override"}}, "194": {"inputs": {"width": 1280, "height": 720, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 32, "crop": "center", "image": ["282", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "282": {"inputs": {"video": "shot2.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 512, "custom_height": 512, "frame_load_cap": 65, "skip_first_frames": 0, "select_every_nth": 1}, "class_type": "VHS_LoadVideo", "_meta": {"title": "Load Video (Upload) 🎥🅥🅗🅢"}}, "283": {"inputs": {"image": "ref.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "284": {"inputs": {"width": ["194", 1], "height": ["194", 2], "upscale_method": "nearest-exact", "keep_proportion": false, "divisible_by": 2, "crop": "disabled", "image": ["308", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "308": {"inputs": {"img_compression": 35, "image": ["283", 0]}, "class_type": "LTXVPreprocess", "_meta": {"title": "LTXVPreprocess"}}, "314": {"inputs": {"indexes": "0", "err_if_missing": true, "err_if_empty": true, "image": ["194", 0]}, "class_type": "VHS_SelectImages", "_meta": {"title": "Select Images 🎥🅥🅗🅢"}}, "315": {"inputs": {"positive": ["318", 0], "negative": ["318", 1], "latent": ["72", 0]}, "class_type": "LTXVCropGuides", "_meta": {"title": "LTXVCropGuides"}}, "316": {"inputs": {"frame_idx": 0, "strength": 1, "positive": ["124", 0], "negative": ["124", 0], "vae": ["44", 2], "latent": ["134", 0], "image": ["314", 0]}, "class_type": "LTXVAddGuide", "_meta": {"title": "LTXVAddGuide"}}, "317": {"inputs": {"positive": ["316", 0], "negative": ["316", 1], "latent": ["122", 0]}, "class_type": "LTXVCropGuides", "_meta": {"title": "LTXVCropGuides"}}, "318": {"inputs": {"frame_idx": 0, "strength": 1, "positive": ["69", 0], "negative": ["69", 1], "vae": ["44", 2], "latent": ["317", 2], "image": ["284", 0]}, "class_type": "LTXVAddGuide", "_meta": {"title": "LTXVAddGuide"}}}, "comfy_fork_version": "HEAD@3d2b9d62"}, "version": 0.4}