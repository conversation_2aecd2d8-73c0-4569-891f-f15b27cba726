[project]
name = "comfyui-ltxvideo"
version = "0.1.0"
description = "Custom nodes for LTX-Video support in ComfyUI"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.10"
readme = "README.md"
license = { file = "LICENSE" }
dependencies = [
    "diffusers",
    "huggingface_hub>=0.25.2",
    "transformers[timm]>=4.45.0",
    "einops",
    "ninja~=********"
]

[project.optional-dependencies]
internal = [
    "ltx-video@git+https://github.com/Lightricks/LTX-Video@ltx-video-0.9.1",
    "av>=10.0.0",
    "q8-kernels==0.0.6"
]
dev = [
    "pre-commit>=4.0.1",
    "pytest>=8.0.0",
    "websocket-client==1.6.1",
    "scikit-image==0.24.0"
]

[tool.isort]
profile = "black"
line_length = 88
force_single_line = false
