# general formatting
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
    -   id: check-yaml
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
    -   id: requirements-txt-fixer
    -   id: check-added-large-files
    -   id: check-toml
# Isort (import sorting)
-   repo: https://github.com/PyCQA/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile, black]
# Black (code formatting)
-   repo: https://github.com/psf/black
    rev: 24.4.2 # Replace by any tag/version: https://github.com/psf/black/tags
    hooks:
      - id: black
        language_version: python3.10
# flake8 linter
-   repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: 'v0.4.4'
    hooks:
      - id: ruff
