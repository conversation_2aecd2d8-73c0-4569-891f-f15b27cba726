accelerate==0.21.0
av==11.0.0
clip @ https://github.com/openai/CLIP/archive/d50d76daa670286dd6cacf3bcd80b5e4823fc8e1.zip#sha256=b5842c25da441d6c581b53a5c60e0c2127ebafe0f746f8e15561a006c6c3be6a
decord==0.6.0
diffusers==0.24.0
einops==0.4.1
gradio==3.41.2
gradio_client==0.5.0
imageio==2.33.0
imageio-ffmpeg==0.4.9
numpy==1.23.5
omegaconf==2.2.3
onnxruntime-gpu==1.16.3
open-clip-torch==2.20.0
opencv-contrib-python==********
opencv-python==********
Pillow==9.5.0
scikit-image==0.21.0
scikit-learn==1.3.2
scipy==1.11.4
torch==2.0.1
torchdiffeq==0.2.3
torchmetrics==1.2.1
torchsde==0.2.5
torchvision==0.15.2
tqdm==4.66.1
transformers==4.30.2
mlflow==2.9.2
xformers==0.0.22
controlnet-aux==0.0.7
ffmpeg-python
soundfile
mediapipe
decord
IPython
scenedetect
