[project]
name = "comfyui_echomimic"
description = "ou can using EchoMimic in comfyui,please using pip install install miss module"
version = "1.1.0"
license = { file = "LICENSE" }
dependencies = ["ffmpeg-python", "mediapipe", "moviepy", "IPython", "av","omegaconf","ultralytics","opencv-python","lpips","torchmetrics","torchtyping","tqdm","torch","torchvision","torchaudio","transformers","diffusers","einops"]

[project.urls]
Repository = "https://github.com/smthemex/ComfyUI_EchoMimic"
#  Used by Comfy Registry https://comfyregistry.org

[tool.comfy]
PublisherId = "smthemex"
DisplayName = "ComfyUI_EchoMimic"
Icon = ""
