#!/bin/bash

# Startup script for Local AI Talking Avatar
echo "🤖 Starting Local AI Talking Avatar..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required directories exist
if [ ! -d "python/venv" ]; then
    print_error "Python virtual environment not found. Please run ./scripts/setup.sh first."
    exit 1
fi

# Check if <PERSON>llama is running
print_info "Checking Ollama status..."
if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    print_warning "Ollama not responding. Starting Ollama..."
    ollama serve &
    sleep 5
    
    if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        print_error "Failed to start Ollama. Please start it manually: ollama serve"
        exit 1
    fi
fi

print_info "Ollama is running ✓"

# Start Python processing server
print_info "Starting Python processing server..."
cd python
source venv/bin/activate
python flask_server.py &
PYTHON_PID=$!
cd ..

# Wait for Python server to start
sleep 3

# Check if Python server is running
if ! curl -s http://localhost:5001/health > /dev/null 2>&1; then
    print_warning "Python server may not have started properly"
fi

# Start Node.js main server
print_info "Starting main application server..."
npm start &
NODE_PID=$!

# Wait for Node.js server to start
sleep 2

echo ""
echo "=================================================="
print_info "🚀 Application started successfully!"
echo ""
echo "📊 Server Status:"
echo "  • Main App:      http://localhost:3001"
echo "  • Python API:    http://localhost:5001"
echo "  • Ollama API:    http://localhost:11434"
echo ""
echo "🎯 Open your browser to: http://localhost:3001"
echo ""
echo "📝 Process IDs:"
echo "  • Python server: $PYTHON_PID"
echo "  • Node.js server: $NODE_PID"
echo ""
print_warning "Press Ctrl+C to stop all servers"
echo "=================================================="

# Function to cleanup on exit
cleanup() {
    echo ""
    print_info "Shutting down servers..."
    kill $PYTHON_PID 2>/dev/null
    kill $NODE_PID 2>/dev/null
    print_info "Servers stopped. Goodbye! 👋"
    exit 0
}

# Set trap to cleanup on interrupt
trap cleanup INT TERM

# Wait for interrupt
wait
