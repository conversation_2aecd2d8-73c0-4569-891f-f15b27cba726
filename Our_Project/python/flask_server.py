#!/usr/bin/env python3
"""
Flask server for Python-based avatar processing
Handles image processing, facial landmarks, and lip sync generation
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import json
import tempfile
import subprocess
from werkzeug.utils import secure_filename
from avatar_processor import AvatarProcessor
import cv2
import numpy as np

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
PROCESSED_FOLDER = 'processed'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'webp'}

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PROCESSED_FOLDER, exist_ok=True)

# Initialize avatar processor
avatar_processor = AvatarProcessor()

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'avatar-processor'})

@app.route('/process-avatar', methods=['POST'])
def process_avatar():
    """
    Process uploaded avatar image to detect facial landmarks
    """
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file type'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)
        
        # Process image to detect landmarks
        landmarks_data = avatar_processor.detect_facial_landmarks(filepath)
        
        if not landmarks_data:
            return jsonify({'error': 'No face detected in image'}), 400
        
        # Save landmarks data
        landmarks_file = os.path.join(PROCESSED_FOLDER, f"{timestamp}_landmarks.json")
        avatar_processor.save_landmarks_data(landmarks_data, landmarks_file)
        
        return jsonify({
            'success': True,
            'filename': filename,
            'landmarks_file': landmarks_file,
            'lip_landmarks_count': len(landmarks_data['lip_landmarks'])
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/generate-lip-sync', methods=['POST'])
def generate_lip_sync():
    """
    Generate lip sync animation frames from audio and viseme data
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        avatar_filename = data.get('avatar_filename')
        audio_path = data.get('audio_path')
        rhubarb_data = data.get('rhubarb_data', [])
        
        if not avatar_filename or not audio_path:
            return jsonify({'error': 'Missing required parameters'}), 400
        
        # Find avatar file
        avatar_path = os.path.join(UPLOAD_FOLDER, avatar_filename)
        if not os.path.exists(avatar_path):
            return jsonify({'error': 'Avatar file not found'}), 404
        
        # Create output directory for frames
        timestamp = str(int(time.time()))
        output_dir = os.path.join(PROCESSED_FOLDER, f"animation_{timestamp}")
        
        # Process lip sync sequence
        frame_paths = avatar_processor.process_lip_sync_sequence(
            avatar_path, rhubarb_data, output_dir
        )
        
        if not frame_paths:
            return jsonify({'error': 'Failed to generate animation frames'}), 500
        
        # Create animation data for frontend
        animation_data = []
        for i, frame_path in enumerate(frame_paths):
            frame_data = rhubarb_data[i] if i < len(rhubarb_data) else {'viseme': 'X', 'intensity': 0}
            animation_data.append({
                'frame_path': frame_path,
                'viseme': frame_data.get('viseme', 'X'),
                'intensity': frame_data.get('intensity', 0),
                'timestamp': frame_data.get('timestamp', i * 33.33)  # 30 FPS
            })
        
        return jsonify({
            'success': True,
            'animation_data': animation_data,
            'frame_count': len(frame_paths),
            'output_directory': output_dir
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/get-frame/<path:frame_path>')
def get_frame(frame_path):
    """
    Serve animation frame images
    """
    try:
        # Security check - ensure path is within processed folder
        if not frame_path.startswith(PROCESSED_FOLDER):
            return jsonify({'error': 'Invalid frame path'}), 403
        
        if not os.path.exists(frame_path):
            return jsonify({'error': 'Frame not found'}), 404
        
        return send_file(frame_path, mimetype='image/jpeg')
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/test-viseme', methods=['POST'])
def test_viseme():
    """
    Test viseme transformation on uploaded image
    """
    try:
        data = request.get_json()
        
        avatar_filename = data.get('avatar_filename')
        viseme = data.get('viseme', 'A')
        intensity = data.get('intensity', 1.0)
        
        if not avatar_filename:
            return jsonify({'error': 'No avatar filename provided'}), 400
        
        avatar_path = os.path.join(UPLOAD_FOLDER, avatar_filename)
        if not os.path.exists(avatar_path):
            return jsonify({'error': 'Avatar file not found'}), 404
        
        # Detect landmarks
        landmarks_data = avatar_processor.detect_facial_landmarks(avatar_path)
        if not landmarks_data:
            return jsonify({'error': 'No face detected'}), 400
        
        # Load and warp image
        original_image = cv2.imread(avatar_path)
        warped_image = avatar_processor.warp_mouth_for_viseme(
            original_image, landmarks_data, viseme, intensity
        )
        
        # Save test result
        timestamp = str(int(time.time()))
        output_path = os.path.join(PROCESSED_FOLDER, f"test_{timestamp}_{viseme}.jpg")
        cv2.imwrite(output_path, warped_image)
        
        return jsonify({
            'success': True,
            'output_path': output_path,
            'viseme': viseme,
            'intensity': intensity
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/cleanup', methods=['POST'])
def cleanup_files():
    """
    Clean up old processed files
    """
    try:
        data = request.get_json()
        max_age_hours = data.get('max_age_hours', 24)
        
        import time
        current_time = time.time()
        cutoff_time = current_time - (max_age_hours * 3600)
        
        cleaned_count = 0
        
        # Clean upload folder
        for filename in os.listdir(UPLOAD_FOLDER):
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            if os.path.getctime(filepath) < cutoff_time:
                os.remove(filepath)
                cleaned_count += 1
        
        # Clean processed folder
        for root, dirs, files in os.walk(PROCESSED_FOLDER):
            for filename in files:
                filepath = os.path.join(root, filename)
                if os.path.getctime(filepath) < cutoff_time:
                    os.remove(filepath)
                    cleaned_count += 1
        
        return jsonify({
            'success': True,
            'cleaned_files': cleaned_count
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    import time
    print("Starting Avatar Processing Server...")
    print(f"Upload folder: {os.path.abspath(UPLOAD_FOLDER)}")
    print(f"Processed folder: {os.path.abspath(PROCESSED_FOLDER)}")
    
    app.run(host='0.0.0.0', port=5001, debug=True)
