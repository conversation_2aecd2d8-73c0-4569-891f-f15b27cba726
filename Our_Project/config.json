{"ollama": {"model": "qwen3:latest", "url": "http://localhost:11434", "timeout": 30000, "max_tokens": 500}, "speech": {"engine": "macos_say", "voice": "<PERSON>", "rate": 180, "output_format": "wav"}, "whisper": {"engine": "superwhisper", "fallback": "<PERSON><PERSON><PERSON><PERSON>", "language": "en", "model": "base"}, "rhubarb": {"path": "./tools/rhubarb/rhubarb", "dialog_file": "temp_dialog.txt", "output_format": "json"}, "python": {"server_port": 5001, "server_host": "localhost"}, "avatar": {"max_image_size": "2048x2048", "supported_formats": ["jpg", "jpeg", "png", "webp"], "face_detection_confidence": 0.5}, "animation": {"frame_rate": 30, "duration_padding": 0.5, "viseme_smoothing": true}, "performance": {"apple_silicon_optimized": true, "max_concurrent_processes": 4, "memory_limit_mb": 2048, "gpu_acceleration": true}, "paths": {"uploads": "./assets/uploads", "processed": "./assets/processed", "audio": "./assets/audio", "temp": "./temp", "logs": "./logs"}, "debug": {"enabled": false, "log_level": "info", "save_intermediate_files": false}}