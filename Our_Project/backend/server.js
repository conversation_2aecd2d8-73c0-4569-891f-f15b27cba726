const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const axios = require('axios');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../frontend')));

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../assets/uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    cb(null, `avatar-${Date.now()}${path.extname(file.originalname)}`);
  }
});

const upload = multer({ storage });

// Routes

// Serve frontend
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// Upload avatar image
app.post('/api/upload-avatar', upload.single('avatar'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file uploaded' });
  }

  res.json({
    success: true,
    filename: req.file.filename,
    path: req.file.path
  });
});

// Process speech-to-text
app.post('/api/speech-to-text', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file uploaded' });
    }

    // Call SuperWhisper or MacWhisper
    const transcription = await processAudioWithWhisper(req.file.path);

    res.json({
      success: true,
      transcription: transcription
    });
  } catch (error) {
    console.error('Speech-to-text error:', error);
    res.status(500).json({ error: 'Failed to process audio' });
  }
});

// Generate AI response
app.post('/api/generate-response', async (req, res) => {
  try {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'No message provided' });
    }

    // Call Ollama
    const response = await generateWithOllama(message);

    res.json({
      success: true,
      response: response
    });
  } catch (error) {
    console.error('AI response error:', error);
    res.status(500).json({ error: 'Failed to generate response' });
  }
});

// Generate speech and lip sync
app.post('/api/generate-speech', async (req, res) => {
  try {
    const { text, avatarFilename } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'No text provided' });
    }

    // Generate speech with macOS TTS
    const audioPath = await generateSpeechWithSay(text);

    // For now, let's just return the audio path and create simple animation data
    // This bypasses the complex Rhubarb/Python pipeline temporarily
    const simpleAnimationData = createSimpleAnimation(text.length);

    res.json({
      success: true,
      audioPath: audioPath.replace(__dirname + '/../', ''), // Make path relative for frontend
      lipSyncData: [],
      animationData: simpleAnimationData
    });
  } catch (error) {
    console.error('Speech generation error:', error);
    res.status(500).json({ error: 'Failed to generate speech' });
  }
});

// Serve audio files
app.get('/audio/:filename', (req, res) => {
  const filename = req.params.filename;
  const audioPath = path.join(__dirname, '../assets/audio', filename);

  if (fs.existsSync(audioPath)) {
    res.sendFile(audioPath);
  } else {
    res.status(404).json({ error: 'Audio file not found' });
  }
});

// Helper functions

function createSimpleAnimation(textLength) {
  // Create a simple animation based on text length
  const duration = Math.max(textLength * 100, 2000); // Rough estimate: 100ms per character, min 2 seconds
  const frameCount = Math.ceil(duration / 33.33); // 30 FPS
  const frames = [];

  const visemes = ['A', 'E', 'I', 'O', 'U', 'B', 'M', 'X'];

  for (let i = 0; i < frameCount; i++) {
    const progress = i / frameCount;
    const visemeIndex = Math.floor(Math.sin(progress * Math.PI * 8) * 3 + 3); // Oscillate between visemes
    const viseme = visemes[visemeIndex] || 'X';

    frames.push({
      frame_index: i,
      viseme: viseme,
      timestamp: i * 33.33,
      intensity: Math.sin(progress * Math.PI * 4) * 0.5 + 0.5
    });
  }

  return frames;
}

async function processAudioWithWhisper(audioPath) {
  try {
    // Load configuration
    const config = JSON.parse(fs.readFileSync(path.join(__dirname, '../config.json'), 'utf8'));
    const whisperConfig = config.whisper;

    // Try SuperWhisper first
    if (whisperConfig.engine === 'superwhisper') {
      try {
        return await processWithSuperWhisper(audioPath);
      } catch (error) {
        console.warn('SuperWhisper failed, trying fallback:', error.message);
      }
    }

    // Try MacWhisper as fallback
    if (whisperConfig.fallback === 'macwhisper') {
      try {
        return await processWithMacWhisper(audioPath);
      } catch (error) {
        console.warn('MacWhisper failed:', error.message);
      }
    }

    // If both fail, use a simple placeholder
    console.warn('All speech recognition methods failed, using placeholder');
    return "I heard you speak, but couldn't transcribe it. Please try typing your message.";

  } catch (error) {
    console.error('Speech-to-text error:', error);
    throw new Error('Failed to process audio');
  }
}

async function processWithSuperWhisper(audioPath) {
  // SuperWhisper integration
  // Note: This requires SuperWhisper to be installed and configured
  return new Promise((resolve, reject) => {
    // Check if SuperWhisper is available
    const superWhisperPath = '/Applications/SuperWhisper.app/Contents/MacOS/SuperWhisper';

    if (!fs.existsSync(superWhisperPath)) {
      reject(new Error('SuperWhisper not found'));
      return;
    }

    // For now, return a placeholder since SuperWhisper API integration
    // requires specific setup that varies by installation
    resolve("SuperWhisper transcription would go here");
  });
}

async function processWithMacWhisper(audioPath) {
  // MacWhisper integration via command line
  return new Promise((resolve, reject) => {
    // Try to use MacWhisper CLI if available
    const macWhisperProcess = spawn('whisper', [audioPath, '--language', 'en']);

    let stdout = '';
    let stderr = '';

    macWhisperProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    macWhisperProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    macWhisperProcess.on('close', (code) => {
      if (code === 0) {
        // Extract transcription from output
        const transcription = extractTranscriptionFromOutput(stdout);
        resolve(transcription || "Could not extract transcription");
      } else {
        reject(new Error(`MacWhisper failed: ${stderr}`));
      }
    });

    macWhisperProcess.on('error', (error) => {
      reject(new Error(`MacWhisper process error: ${error.message}`));
    });
  });
}

function extractTranscriptionFromOutput(output) {
  // Extract transcription text from whisper output
  // This is a simplified version - actual implementation depends on output format
  const lines = output.split('\n');
  for (const line of lines) {
    if (line.trim() && !line.includes('[') && !line.includes('Whisper')) {
      return line.trim();
    }
  }
  return null;
}

async function generateWithOllama(message) {
  try {
    // Load configuration
    const config = JSON.parse(fs.readFileSync(path.join(__dirname, '../config.json'), 'utf8'));
    const ollamaConfig = config.ollama;

    // Create a conversational prompt
    const conversationalPrompt = `You are a friendly AI assistant. Please respond naturally and conversationally to the following message. Keep your response concise but engaging.

User: ${message}
Assistant: `;

    const response = await axios.post(`${ollamaConfig.url}/api/generate`, {
      model: ollamaConfig.model,
      prompt: conversationalPrompt,
      stream: false,
      options: {
        temperature: 0.7,
        top_p: 0.9,
        max_tokens: ollamaConfig.max_tokens || 500
      }
    }, {
      timeout: ollamaConfig.timeout || 30000
    });

    // Clean up the response by removing thinking tags and extra whitespace
    let cleanResponse = response.data.response.trim();

    // Remove <think>...</think> blocks
    cleanResponse = cleanResponse.replace(/<think>[\s\S]*?<\/think>/g, '');

    // Clean up any extra whitespace
    cleanResponse = cleanResponse.trim();

    return cleanResponse;
  } catch (error) {
    console.error('Ollama error:', error);
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Ollama server is not running. Please start it with: ollama serve');
    }
    throw new Error('Failed to generate response with Ollama');
  }
}

async function generateSpeechWithSay(text) {
  try {
    // Load configuration
    const config = JSON.parse(fs.readFileSync(path.join(__dirname, '../config.json'), 'utf8'));
    const speechConfig = config.speech;

    // Create unique filename
    const timestamp = Date.now();
    const audioDir = path.join(__dirname, '../assets/audio');

    // Ensure audio directory exists
    if (!fs.existsSync(audioDir)) {
      fs.mkdirSync(audioDir, { recursive: true });
    }

    const audioPath = path.join(audioDir, `speech_${timestamp}.wav`);

    // Prepare say command
    const sayCommand = [
      'say',
      '-v', speechConfig.voice || 'Samantha',
      '-r', (speechConfig.rate || 200).toString(),
      '-o', audioPath,
      '--data-format=LEF32@22050',
      text
    ];

    // Execute say command
    return new Promise((resolve, reject) => {
      const sayProcess = spawn(sayCommand[0], sayCommand.slice(1));

      sayProcess.on('close', (code) => {
        if (code === 0 && fs.existsSync(audioPath)) {
          resolve(audioPath);
        } else {
          reject(new Error(`Say command failed with code ${code}`));
        }
      });

      sayProcess.on('error', (error) => {
        reject(new Error(`Say command error: ${error.message}`));
      });
    });

  } catch (error) {
    console.error('TTS error:', error);
    throw new Error('Failed to generate speech');
  }
}

async function generateLipSync(audioPath) {
  try {
    // Load configuration
    const config = JSON.parse(fs.readFileSync(path.join(__dirname, '../config.json'), 'utf8'));
    const rhubarbConfig = config.rhubarb;

    // Check if Rhubarb exists
    const rhubarbPath = path.join(__dirname, '..', rhubarbConfig.path);
    if (!fs.existsSync(rhubarbPath)) {
      throw new Error('Rhubarb Lip Sync not found. Please run setup script.');
    }

    // Create output path for lip sync data
    const timestamp = Date.now();
    const outputPath = path.join(__dirname, '../temp', `lipsync_${timestamp}.json`);

    // Ensure temp directory exists
    const tempDir = path.dirname(outputPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Run Rhubarb
    return new Promise((resolve, reject) => {
      const rhubarbProcess = spawn(rhubarbPath, [
        '-f', 'json',
        '-o', outputPath,
        audioPath
      ]);

      let stderr = '';
      rhubarbProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      rhubarbProcess.on('close', (code) => {
        if (code === 0 && fs.existsSync(outputPath)) {
          try {
            const lipSyncData = JSON.parse(fs.readFileSync(outputPath, 'utf8'));

            // Convert Rhubarb format to our format
            const processedData = processMouthCues(lipSyncData);

            // Clean up temp file
            fs.unlinkSync(outputPath);

            resolve(processedData);
          } catch (parseError) {
            reject(new Error(`Failed to parse lip sync data: ${parseError.message}`));
          }
        } else {
          reject(new Error(`Rhubarb failed with code ${code}: ${stderr}`));
        }
      });

      rhubarbProcess.on('error', (error) => {
        reject(new Error(`Rhubarb process error: ${error.message}`));
      });
    });

  } catch (error) {
    console.error('Lip sync error:', error);
    throw new Error('Failed to generate lip sync data');
  }
}

function processMouthCues(rhubarbData) {
  // Convert Rhubarb mouth cues to our animation format
  const processedFrames = [];

  if (rhubarbData.mouthCues) {
    for (const cue of rhubarbData.mouthCues) {
      processedFrames.push({
        viseme: cue.value,
        timestamp: cue.start * 1000, // Convert to milliseconds
        duration: (cue.end - cue.start) * 1000,
        intensity: 1.0
      });
    }
  }

  return processedFrames;
}

async function generateAvatarAnimation(avatarFilename, lipSyncData, audioPath) {
  try {
    // Call Python server to generate animation frames
    const pythonServerUrl = 'http://localhost:5001';

    const response = await axios.post(`${pythonServerUrl}/generate-lip-sync`, {
      avatar_filename: avatarFilename,
      audio_path: audioPath,
      rhubarb_data: lipSyncData
    });

    if (response.data.success) {
      return response.data.animation_data;
    } else {
      throw new Error('Python server failed to generate animation');
    }

  } catch (error) {
    console.error('Avatar animation error:', error);
    // Return simplified animation data as fallback
    return lipSyncData.map((frame, index) => ({
      frame_index: index,
      viseme: frame.viseme,
      timestamp: frame.timestamp,
      intensity: frame.intensity
    }));
  }
}

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('Make sure Ollama is running on port 11434');
});
