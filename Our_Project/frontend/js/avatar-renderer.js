// Avatar rendering and animation using HTML5 Canvas
class AvatarRenderer {
    constructor() {
        this.canvas = document.getElementById('avatarCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.avatarImage = null;
        this.originalImageData = null;
        this.isAnimating = false;
        this.animationFrameId = null;
        
        // Animation properties
        this.currentFrame = 0;
        this.animationFrames = [];
        this.frameRate = 30; // FPS
        this.frameDuration = 1000 / this.frameRate;
        this.lastFrameTime = 0;
    }

    async loadAvatar(imageFile) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.avatarImage = img;
                this.setupCanvas();
                this.drawStaticAvatar();
                resolve();
            };
            img.onerror = reject;
            img.src = URL.createObjectURL(imageFile);
        });
    }

    setupCanvas() {
        if (!this.avatarImage) return;

        // Set canvas size to maintain aspect ratio
        const maxSize = 400;
        const aspectRatio = this.avatarImage.width / this.avatarImage.height;
        
        if (aspectRatio > 1) {
            this.canvas.width = maxSize;
            this.canvas.height = maxSize / aspectRatio;
        } else {
            this.canvas.width = maxSize * aspectRatio;
            this.canvas.height = maxSize;
        }

        // Store original image data
        this.drawStaticAvatar();
        this.originalImageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    }

    drawStaticAvatar() {
        if (!this.avatarImage) return;

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(this.avatarImage, 0, 0, this.canvas.width, this.canvas.height);
    }

    async playAnimation(animationData, audioPath) {
        if (!animationData || animationData.length === 0) {
            console.warn('No animation data provided');
            return;
        }

        try {
            // Load and play audio - construct full URL for audio file
            const audioUrl = `http://localhost:3001/audio/${audioPath}`;
            console.log('Playing audio:', audioUrl);
            const audio = new Audio(audioUrl);
            
            // Prepare animation frames
            this.animationFrames = animationData;
            this.currentFrame = 0;
            this.isAnimating = true;

            // Start animation
            const startTime = performance.now();
            this.lastFrameTime = startTime;

            // Add error handling for audio
            audio.addEventListener('error', (e) => {
                console.error('Audio loading error:', e);
                console.error('Audio error details:', audio.error);
            });

            audio.addEventListener('canplaythrough', () => {
                console.log('Audio can play through');
            });

            // Play audio with error handling
            try {
                await audio.play();
                console.log('Audio playback started successfully');
            } catch (e) {
                console.error('Audio play failed:', e);
                // Continue with animation even if audio fails
            }

            // Start simple talking animation
            this.startTalkingAnimation();

            // Stop animation when audio ends
            audio.addEventListener('ended', () => {
                this.stopTalkingAnimation();
            });

        } catch (error) {
            console.error('Animation playback error:', error);
            this.stopTalkingAnimation();
        }
    }

    startTalkingAnimation() {
        if (this.talkingInterval) {
            clearInterval(this.talkingInterval);
        }

        this.isTalking = true;
        let talkPhase = 0;

        console.log('Starting talking animation');

        // Create visible talking animation
        this.talkingInterval = setInterval(() => {
            if (!this.isTalking) return;

            talkPhase += 0.5;
            this.drawTalkingAvatar(talkPhase);
        }, 100); // 10 FPS for visible animation
    }

    stopTalkingAnimation() {
        this.isTalking = false;
        if (this.talkingInterval) {
            clearInterval(this.talkingInterval);
            this.talkingInterval = null;
        }
        // Return to static avatar
        this.drawStaticAvatar();
    }

    drawTalkingAvatar(talkPhase) {
        if (!this.avatarImage) return;

        // Clear and redraw everything
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Calculate animation values - make them more dramatic
        const intensity = Math.abs(Math.sin(talkPhase)) * 0.9 + 0.1; // 0.1 to 1.0
        const scale = 1 + intensity * 0.15; // More dramatic scaling
        const bounceY = Math.sin(talkPhase * 2) * 8; // Vertical bounce
        const wiggleX = Math.sin(talkPhase * 3) * 4; // Horizontal wiggle

        // Draw background glow effect
        this.ctx.save();
        this.ctx.shadowColor = '#10b981';
        this.ctx.shadowBlur = 20 * intensity;
        this.ctx.fillStyle = `rgba(16, 185, 129, ${intensity * 0.1})`;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.restore();

        // Save context for transformations
        this.ctx.save();

        // Move to center for scaling and add movement
        this.ctx.translate(
            this.canvas.width / 2 + wiggleX,
            this.canvas.height / 2 + bounceY
        );
        this.ctx.scale(scale, scale);

        // Draw avatar with transformations
        this.ctx.drawImage(
            this.avatarImage,
            -this.canvas.width / 2,
            -this.canvas.height / 2,
            this.canvas.width,
            this.canvas.height
        );

        this.ctx.restore();

        // Add VERY visible talking indicators
        this.ctx.save();

        // Thick pulsing border
        this.ctx.strokeStyle = `rgba(16, 185, 129, ${intensity})`;
        this.ctx.lineWidth = 8;
        this.ctx.strokeRect(4, 4, this.canvas.width - 8, this.canvas.height - 8);

        // Large speaking indicator with background
        this.ctx.fillStyle = `rgba(0, 0, 0, 0.7)`;
        this.ctx.fillRect(0, 0, this.canvas.width, 40);

        this.ctx.fillStyle = `rgba(16, 185, 129, ${intensity})`;
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('🗣️ SPEAKING', this.canvas.width / 2, 28);

        // Multiple sound wave rings
        for (let i = 0; i < 5; i++) {
            const waveRadius = 30 + i * 20 + intensity * 15;
            this.ctx.beginPath();
            this.ctx.arc(this.canvas.width / 2, this.canvas.height / 2, waveRadius, 0, 2 * Math.PI);
            this.ctx.strokeStyle = `rgba(16, 185, 129, ${(1 - i * 0.15) * intensity * 0.4})`;
            this.ctx.lineWidth = 3;
            this.ctx.stroke();
        }

        // Corner indicators
        const cornerSize = 20 * intensity;
        this.ctx.fillStyle = `rgba(16, 185, 129, ${intensity})`;
        this.ctx.fillRect(0, 0, cornerSize, cornerSize);
        this.ctx.fillRect(this.canvas.width - cornerSize, 0, cornerSize, cornerSize);
        this.ctx.fillRect(0, this.canvas.height - cornerSize, cornerSize, cornerSize);
        this.ctx.fillRect(this.canvas.width - cornerSize, this.canvas.height - cornerSize, cornerSize, cornerSize);

        this.ctx.restore();
    }

    animateFrame(currentTime) {
        if (!this.isAnimating) return;

        // Calculate if it's time for next frame
        const deltaTime = currentTime - this.lastFrameTime;
        
        if (deltaTime >= this.frameDuration) {
            this.renderFrame();
            this.currentFrame++;
            this.lastFrameTime = currentTime;

            // Check if animation is complete
            if (this.currentFrame >= this.animationFrames.length) {
                this.stopAnimation();
                return;
            }
        }

        this.animationFrameId = requestAnimationFrame((time) => this.animateFrame(time));
    }

    renderFrame() {
        if (!this.originalImageData || this.currentFrame >= this.animationFrames.length) {
            return;
        }

        // Get current frame data
        const frameData = this.animationFrames[this.currentFrame];
        
        // Restore original image
        this.ctx.putImageData(this.originalImageData, 0, 0);

        // Apply frame transformations
        this.applyFrameTransformations(frameData);
    }

    applyFrameTransformations(frameData) {
        // This is a simplified version - in the full implementation,
        // this would apply the actual lip sync transformations
        // based on the viseme data from Rhubarb
        
        if (!frameData || !frameData.viseme) return;

        const viseme = frameData.viseme;
        const intensity = frameData.intensity || 1.0;

        // Apply basic mouth shape transformations based on viseme
        this.applyMouthShape(viseme, intensity);
    }

    applyMouthShape(viseme, intensity) {
        // Simplified mouth shape application
        // In the full implementation, this would use the facial landmarks
        // and OpenCV-style transformations
        
        const mouthShapes = {
            'A': { openness: 0.8, width: 1.0 },
            'B': { openness: 0.0, width: 0.8 },
            'C': { openness: 0.6, width: 0.9 },
            'D': { openness: 0.4, width: 1.0 },
            'E': { openness: 0.3, width: 1.1 },
            'F': { openness: 0.1, width: 0.9 },
            'G': { openness: 0.2, width: 0.8 },
            'H': { openness: 0.3, width: 1.0 },
            'X': { openness: 0.0, width: 1.0 } // Rest position
        };

        const shape = mouthShapes[viseme] || mouthShapes['X'];
        
        // Apply visual feedback (simplified)
        this.drawMouthIndicator(shape, intensity);
    }

    drawMouthIndicator(shape, intensity) {
        // Draw a simple mouth indicator for demonstration
        // In the full implementation, this would be replaced with actual image warping
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height * 0.75; // Approximate mouth position
        
        this.ctx.save();
        this.ctx.globalAlpha = 0.3;
        this.ctx.fillStyle = '#ff6b6b';
        
        // Draw mouth shape indicator
        const width = 30 * shape.width * intensity;
        const height = 15 * shape.openness * intensity;
        
        this.ctx.beginPath();
        this.ctx.ellipse(centerX, centerY, width, height, 0, 0, 2 * Math.PI);
        this.ctx.fill();
        
        this.ctx.restore();
    }

    stopAnimation() {
        this.isAnimating = false;
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        
        // Return to static avatar
        setTimeout(() => {
            this.drawStaticAvatar();
        }, 100);
    }

    // Utility method to create simple test animation
    createTestAnimation(duration = 3000) {
        const frames = [];
        const frameCount = Math.floor(duration / this.frameDuration);
        const visemes = ['A', 'E', 'I', 'O', 'U', 'B', 'M', 'X'];
        
        for (let i = 0; i < frameCount; i++) {
            const progress = i / frameCount;
            const visemeIndex = Math.floor(progress * visemes.length);
            const viseme = visemes[visemeIndex] || 'X';
            
            frames.push({
                viseme: viseme,
                intensity: Math.sin(progress * Math.PI * 4) * 0.5 + 0.5,
                timestamp: i * this.frameDuration
            });
        }
        
        return frames;
    }
}
