<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local AI Talking Avatar</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 Local AI Talking Avatar</h1>
            <p>100% Local • No Cloud Dependencies</p>
        </header>

        <main>
            <!-- Avatar Upload Section -->
            <section class="upload-section">
                <h2>1. Upload Your Avatar Image</h2>
                <div class="upload-area" id="uploadArea">
                    <input type="file" id="avatarInput" accept="image/*" hidden>
                    <div class="upload-placeholder">
                        <div class="upload-icon">📷</div>
                        <p>Click to upload a frontal face photo</p>
                        <small>Supported: JPG, PNG, WebP</small>
                    </div>
                </div>
                <div class="avatar-preview" id="avatarPreview" style="display: none;">
                    <img id="avatarImage" alt="Avatar">
                    <button id="changeAvatar" class="btn-secondary">Change Image</button>
                </div>
            </section>

            <!-- Avatar Display Section -->
            <section class="avatar-section">
                <h2>2. Your AI Avatar</h2>
                <div class="avatar-container">
                    <canvas id="avatarCanvas" width="400" height="400"></canvas>
                    <div class="avatar-status" id="avatarStatus">
                        Upload an image to get started
                    </div>
                </div>
            </section>

            <!-- Conversation Section -->
            <section class="conversation-section">
                <h2>3. Start Conversation</h2>
                
                <!-- Audio Recording -->
                <div class="audio-controls">
                    <button id="recordBtn" class="btn-primary" disabled>
                        <span class="record-icon">🎤</span>
                        <span class="record-text">Hold to Record</span>
                    </button>
                    <div class="recording-status" id="recordingStatus"></div>
                </div>

                <!-- Text Input Alternative -->
                <div class="text-input-section">
                    <div class="input-group">
                        <input type="text" id="textInput" placeholder="Or type your message here..." disabled>
                        <button id="sendBtn" class="btn-primary" disabled>Send</button>
                    </div>
                </div>

                <!-- Conversation History -->
                <div class="conversation-history" id="conversationHistory">
                    <div class="message-placeholder">
                        Your conversation will appear here...
                    </div>
                </div>
            </section>

            <!-- Status Section -->
            <section class="status-section">
                <h3>System Status</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Ollama (LLM)</span>
                        <span class="status-indicator" id="ollamaStatus">⚪ Checking...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Speech Recognition</span>
                        <span class="status-indicator" id="speechStatus">⚪ Ready</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Avatar Processing</span>
                        <span class="status-indicator" id="avatarProcessingStatus">⚪ Waiting</span>
                    </div>
                </div>

                <!-- Voice Selection -->
                <div class="voice-selector">
                    <label for="voiceSelect">AI Voice:</label>
                    <select id="voiceSelect">
                        <option value="Moira" selected>Moira (Irish Female)</option>
                        <option value="Daniel">Daniel (British Male)</option>
                        <option value="Samantha">Samantha (US Female)</option>
                        <option value="Karen">Karen (Australian Female)</option>
                        <option value="Tessa">Tessa (South African Female)</option>
                        <option value="Alex">Alex (US Male)</option>
                        <option value="Flo (English (US))">Flo (US Female)</option>
                        <option value="Flo (English (UK))">Flo (UK Female)</option>
                        <option value="Eddy (English (US))">Eddy (US Male)</option>
                        <option value="Eddy (English (UK))">Eddy (UK Male)</option>
                    </select>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="js/avatar-renderer.js"></script>
    <script src="js/audio-recorder.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
