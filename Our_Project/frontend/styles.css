/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

/* Upload Section */
.upload-area {
    border: 3px dashed #cbd5e0;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #f7fafc;
}

.upload-area.dragover {
    border-color: #667eea;
    background-color: #ebf8ff;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-placeholder p {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 5px;
}

.upload-placeholder small {
    color: #718096;
}

.avatar-preview {
    text-align: center;
}

.avatar-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Avatar Section */
.avatar-container {
    text-align: center;
}

#avatarCanvas {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: #f7fafc;
    max-width: 100%;
    height: auto;
}

.avatar-status {
    margin-top: 15px;
    padding: 10px;
    background: #f7fafc;
    border-radius: 8px;
    color: #4a5568;
    font-size: 0.9rem;
}

/* Conversation Section */
.conversation-section {
    grid-column: 1 / -1;
}

.audio-controls {
    text-align: center;
    margin-bottom: 20px;
}

#recordBtn {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

#recordBtn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.record-icon {
    font-size: 1.3rem;
}

.recording-status {
    margin-top: 10px;
    font-size: 0.9rem;
    color: #718096;
}

.text-input-section {
    margin-bottom: 20px;
}

.input-group {
    display: flex;
    gap: 10px;
}

#textInput {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
}

#textInput:focus {
    outline: none;
    border-color: #667eea;
}

.conversation-history {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    background: #f7fafc;
}

.message-placeholder {
    text-align: center;
    color: #718096;
    font-style: italic;
}

.message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
}

.message.user {
    background: #667eea;
    color: white;
    margin-left: 20%;
}

.message.ai {
    background: #48bb78;
    color: white;
    margin-right: 20%;
}

/* Status Section */
.status-section {
    grid-column: 1 / -1;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f7fafc;
    border-radius: 8px;
}

.status-label {
    font-weight: 500;
    color: #4a5568;
}

.status-indicator {
    font-size: 0.9rem;
}

/* Buttons */
.btn-primary {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s ease;
}

.btn-primary:hover:not(:disabled) {
    background: #5a67d8;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

/* Responsive Design */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
    }
}

/* Voice selector */
.voice-selector {
    margin-top: 20px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.voice-selector label {
    display: block;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.voice-selector select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #cbd5e0;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #2d3748;
}

.voice-selector select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
