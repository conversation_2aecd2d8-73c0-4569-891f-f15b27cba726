<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Test</title>
</head>
<body>
    <h1>Audio Test Page</h1>
    <button onclick="testAudio()">Test Audio Playback</button>
    <div id="status"></div>
    
    <script>
        function testAudio() {
            const status = document.getElementById('status');
            status.innerHTML = 'Testing audio...';
            
            // Test with a known audio file
            const audioUrl = 'http://localhost:3001/audio/speech_1751605910355.wav';
            console.log('Testing audio URL:', audioUrl);
            
            const audio = new Audio(audioUrl);
            
            audio.addEventListener('loadeddata', () => {
                console.log('Audio loaded successfully');
                status.innerHTML += '<br>✅ Audio loaded successfully';
            });
            
            audio.addEventListener('canplaythrough', () => {
                console.log('Audio can play through');
                status.innerHTML += '<br>✅ Audio can play through';
            });
            
            audio.addEventListener('error', (e) => {
                console.error('Audio error:', e);
                console.error('Audio error details:', audio.error);
                status.innerHTML += '<br>❌ Audio error: ' + (audio.error ? audio.error.message : 'Unknown error');
            });
            
            audio.addEventListener('ended', () => {
                console.log('Audio playback ended');
                status.innerHTML += '<br>✅ Audio playback completed';
            });
            
            // Try to play
            audio.play().then(() => {
                console.log('Audio playback started');
                status.innerHTML += '<br>✅ Audio playback started';
            }).catch(e => {
                console.error('Audio play failed:', e);
                status.innerHTML += '<br>❌ Audio play failed: ' + e.message;
            });
        }
    </script>
</body>
</html>
